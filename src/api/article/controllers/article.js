'use strict';

/**
 * article controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::article.article',
    ({ strapi }) => ({
        async findOne(ctx) {
            const { slug } = ctx.params;
            var svc = await strapi.db.query('api::article.article').findOne({
                populate: {
                    cover: true,
                    image: true,
                    genetica_image: true,
                    sub_package: {
                        populate: {
                            package: true,
                            services: true,
                        }
                    },
                },
                where: {
                    slug
                }
            });

            return {
                service: svc
            };
        },
    }));
