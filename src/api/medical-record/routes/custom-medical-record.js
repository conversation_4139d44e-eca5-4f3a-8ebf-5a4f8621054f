module.exports = {
    routes: [
      {
        method: "POST",
        path: "/medical-record/download-report",
        handler: "medical-record.downloadReport",
        config: {
          policies: [],
          prefix: "",
        },
      },
      {
        method: "POST",
        path: "/medical-record/search",
        handler: "medical-record.search",
        config: {
          policies: [],
          prefix: "",
        },
      },
      {
        method: "POST",
        path: "/medical-record/getRelatedMedicalRecords",
        handler: "medical-record.getRelatedMedicalRecords",
      },
      {
        method: "POST",
        path: "/medical-record/getRelatedPrescriptions",
        handler: "medical-record.getRelatedPrescriptions",
      },
      {
        method: "GET",
        path: "/medical-record/findMedicalByUID/:code",
        handler: "medical-record.findMedicalByUID",
      },
      {
        method: "POST",
        path: "/medical-record/updateMedicalRecordStatus/:id",
        handler: "medical-record.updateMedicalRecordStatus",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/medical-record/createOrUpdateTreatment",
        handler: "medical-record.createOrUpdateTreatment",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/medical-record/addProduct",
        handler: "medical-record.addProduct",
        config: {
          policies: [],
          prefix: false,
        },
      },
    ]
}