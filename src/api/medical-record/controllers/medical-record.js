'use strict';

/**
 * medical-record controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const fs = require('fs');
const dayjs = require("dayjs");
const _ = require("lodash");

const getValueJson = (value) => {
    let js;
    try {
        js = JSON.parse(value);
    } catch (e) {
        return value;
    } finally {
    }
    if (!js) return "";
    if (!Array.isArray(js)) return "";
    return js.map(j => j.value).join(",");
}

module.exports = createCoreController('api::medical-record.medical-record', ({ strapi }) => ({

    async findMedicalByUID(ctx) {
        const { code } = ctx.params;
        let mr = await strapi.query("api::medical-record.medical-record").findOne({
            where: { uid: code },
            populate: {
                patient: true,
            }
        });

        return mr;
    },

    async updateMedicalRecordStatus(ctx) {
        const { id } = ctx.params;

        let mr = await strapi.query("api::medical-record.medical-record")
            .update({
                where: { id },
                data: {
                    status: ctx.request.body.status,
                }
            });

        return mr;
    },

    async createOrUpdateTreatment(ctx) {
        const { id } = ctx.state.user;
        const user = await strapi
            .query("plugin::users-permissions.user")
            .findOne({
                where: { id },
                populate: {
                    role: true,
                }
            });

        const { booking: bid } = ctx.request.body.data;
        const booking = await strapi
            .query("api::booking.booking")
            .findOne({
                where: { id: bid }, populate: {
                    medical_record: true
                }
            });
        ctx.request.body.data.publishedAt = new Date();
        ctx.request.body.data.edit_by = id;

        //refine data
        if (!ctx.request.body.data.temperature) {
            delete ctx.request.body.data.temperature;
        }
        if (!ctx.request.body.data.blood_pressure) {
            delete ctx.request.body.data.blood_pressure;
        }
        if (!ctx.request.body.data.respiratory_rate) {
            delete ctx.request.body.data.respiratory_rate;
        }
        if (!ctx.request.body.data.height) {
            delete ctx.request.body.data.height;
        }
        if (!ctx.request.body.data.weight) {
            delete ctx.request.body.data.weight;
        }
        if (!ctx.request.body.data.bmi) {
            delete ctx.request.body.data.bmi;
        }
        if (!ctx.request.body.data.spo2) {
            delete ctx.request.body.data.spo2;
        }
        if (!ctx.request.body.data.blood_pressure2) {
            delete ctx.request.body.data.blood_pressure2;
        }
        if (!ctx.request.body.data.blood_pressure_1) {
            delete ctx.request.body.data.blood_pressure_1;
        }
        if (!ctx.request.body.data.blood_pressure2_1) {
            delete ctx.request.body.data.blood_pressure2_1;
        }

        let result;
        if (booking.medical_record) {
            result = await strapi
                .query('api::medical-record.medical-record')
                .update({
                    where: { id: booking.medical_record.id },
                    data: ctx.request.body.data
                });
        } else {
            result = await strapi
                .query('api::medical-record.medical-record')
                .create({
                    data: ctx.request.body.data,
                });
        }

        return result;
    },

    async getRelatedPrescriptions(ctx) {
        const { id } = ctx.state.user;
        const user = await strapi
            .query("plugin::users-permissions.user")
            .findOne({
                where: { id }, populate: {
                    patient: {
                        populate: {
                            relationships: {
                                populate: {
                                    patient: true
                                }
                            },
                        }
                    }
                }
            });

        user.patient.relationships.forEach(async r => {
            let mrs = await strapi.db.query("api::medical-record.medical-record").findMany({
                where: {
                    $and: [
                        { patient: r.patient.id },
                        {
                            booking: {
                                id: {
                                    $gte: 1
                                }
                            }
                        }
                    ]
                },
                populate: {
                    prescriptions: {
                        populate: {
                            Drugs: true
                        }
                    },
                    patient: true,
                    booking: true,
                }
            });

            r.medicalRecords = mrs;
        })

        let mrs = await strapi.db.query("api::medical-record.medical-record").findMany({
            where: {
                $and: [{
                    patient: user.patient.id
                },
                {
                    booking: {
                        id: {
                            $gte: 1
                        }
                    }
                }
                ]
            },
            populate: {
                patient: true,
                prescriptions: true,
            }
        });

        user.medicalRecords = mrs;

        return user;
    },

    async getRelatedMedicalRecords(ctx) {
        const { id } = ctx.state.user;
        const user = await strapi
            .query("plugin::users-permissions.user")
            .findOne({
                where: { id }, populate: {
                    patient: {
                        populate: {
                            relationships: {
                                populate: {
                                    patient: true
                                }
                            },
                        }
                    }
                }
            });

        user.patient.relationships.forEach(async r => {
            let mrs = await strapi.db.query("api::medical-record.medical-record").findMany({
                where: {
                    $and: [
                        { patient: r.patient.id },
                        {
                            booking: {
                                id: {
                                    $gte: 1
                                }
                            }
                        }
                    ]
                },
                populate: {
                    patient: true,
                    booking: true,
                }
            });

            r.medicalRecords = mrs;
        })

        let mrs = await strapi.db.query("api::medical-record.medical-record").findMany({
            where: {
                $and: [{
                    patient: user.patient.id
                },
                {
                    booking: {
                        id: {
                            $gte: 1
                        }
                    }
                }
                ]
            },
            populate: {
                patient: true,
                booking: true,
            }
        });

        user.medicalRecords = mrs;

        return user;
    },

    async downloadReport(ctx) {
        let csv = fs.readFileSync(`${__dirname}/medical-reports.csv`, 'utf8');
        csv = csv.replace("[START_DATE]", dayjs(ctx.request.body.data.startDate).add(7, 'hour').utc().format("DD/MM/YYYY"));
        csv = csv.replace("[END_DATE]", dayjs(ctx.request.body.data.endDate).add(7, 'hour').utc().format("DD/MM/YYYY"));
        let where = {
            createdAt: {
                $gte: new Date(ctx.request.body.data.startDate),
                $lte: new Date(ctx.request.body.data.endDate),
            },
        };

        let mrs = await strapi.query("api::medical-record.medical-record").findMany({
            where,
            populate: {
                patient: true,
            }
        });

        mrs.forEach(m => {
            const address =
                (m.patient?.address?.address ?? "") + ", " +
                (m.patient?.address?.ward?.name ?? "") + ", " +
                (m.patient?.address?.district?.name ?? "") + ", " +
                (m.patient?.address?.province?.name ?? "");

            csv = csv + '\n';
            csv = csv + m.uid + ','; // id
            csv = csv + dayjs(m.createdAt).utc().format("YYYY") + ",";
            csv = csv + m.patient?.membership + ",";
            csv = csv + m.patient?.full_name + ",";
            csv = csv + dayjs(m.birthday).utc().format("YYYY") + ",";
            csv = csv + m.patient?.gender + ",";
            csv = csv + '"' + address + '"' + ",";
            csv = csv + m.patient?.phone + ",";
            csv = csv + m?.circuit + ",";
            csv = csv + m.temperature + ",";
            csv = csv + m.blood_pressure + "/" + m.blood_pressure2 + ",";
            csv = csv + m.blood_pressure_1 + "/" + m.blood_pressure2_1 + ",";
            csv = csv + m.respiratory_rate + ",";
            csv = csv + m.height + ",";
            csv = csv + m.weight + ",";
            csv = csv + m.bmi + ",";
            csv = csv + m.spo2 + ",";
            csv = csv + '"' + getValueJson(m.reasons_to_get_hospitalized) + '"' + ",";
            csv = csv + '"' + getValueJson(m.inquiry) + '"' + ",";
            csv = csv + '"' + getValueJson(m.premise) + '"' + ",";
            csv = csv + '"' + getValueJson(m.general_examination) + '"' + ",";
            csv = csv + '"' + getValueJson(m.examination) + '"' + ",";
            csv = csv + '"' + getValueJson(m.main_diagnose) + '"' + ",";
            csv = csv + '"' + getValueJson(m.other_diagnose) + '"' + ",";
            csv = csv + '"' + getValueJson(m.treatment_regimen) + '"' + ",";
            csv = csv + '"' + (typeof m.services == "string" ? JSON.parse(m.services) : m.services)?.map(s => s.attributes.label).join(',') + '"' + ",";
            csv = csv + '"' + (typeof m.bundle_services == "string" ? JSON.parse(m.bundle_services) : m.bundle_services)?.map(s => s.attributes.label).join(',') + '"';
        })

        return csv;
    },

    async search(ctx) {
        let where = { $and: [] };
        if (ctx.request.body.data.startDate && ctx.request.body.data.endDate) {
            where.$and.push({
                createdAt: {
                    $gte: new Date(ctx.request.body.data.startDate),
                    $lte: new Date(ctx.request.body.data.endDate),
                }
            });
        }
        if (ctx.request.body.data.searchKey) {
            where.$and.push({
                $or: [
                    { uid: { $contains: ctx.request.body.data.searchKey } },
                    { patient: { uid: { $contains: ctx.request.body.data.searchKey } } }
                ]
            });
        }

        let mrs = await strapi.query("api::medical-record.medical-record").findMany({
            where,
            populate: {
                patient: {
                    populate: {
                        patient_source: {
                            populate: {
                                image: true,
                            }
                        }
                    },
                },
                booking: true,
                doctor_in_charge: true,
            },
            orderBy: {
                createdAt: "desc",
            }
        });

        return mrs;
    },

    async addProduct(ctx) {
        const { id, productId } = ctx.request.body;

        let mr = await strapi.query("api::medical-record.medical-record").findOne({
            where: { id },
            populate: {
                prescriptions: {
                    populate: {
                        additional_drugs: {
                            populate: {
                                drug: true,
                            }
                        },
                    }
                }
            }
        });

        let product = await strapi.query("api::product.product").findOne({
            where: { id: productId },
            populate: {
                medicines: {
                    populate: {
                        drug: true,
                    }
                }
            }
        });


        if (!mr.prescriptions || (Array.isArray(mr.prescriptions) && mr.prescriptions.length == 0)) {
            await strapi.entityService.create("api::prescription.prescription", {
                data: {
                    medical_record: id,
                    additional_drugs: product.medicines.map(m => {
                        return {
                            drug: m.drug.id,
                        }
                    }),
                    publishedAt: new Date(),
                }
            });
        } else {
            const pr = mr.prescriptions[0];

            await strapi.entityService.update("api::prescription.prescription", pr.id, {
                data: {
                    ...pr,
                    additional_drugs:
                        [
                            ...pr.additional_drugs.map(m => {
                                return {
                                    ...m,
                                    drug: m.drug.id,
                                }
                            }),
                            ...product.medicines.map(m => {
                                return {
                                    drug: m.drug.id,
                                    amount: 0,
                                    morningAmount: 0,
                                    noonAmount: 0,
                                    afternoonAmount: 0,
                                    eveningAmount: 0,
                                    unit: "",
                                    usage: "",
                                    numberOfDays: 0,
                                }
                            })]
                }
            });
        }

        return mr;
    }
}
));
