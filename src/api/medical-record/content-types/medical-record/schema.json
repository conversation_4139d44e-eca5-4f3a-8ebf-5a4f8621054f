{"kind": "collectionType", "collectionName": "medical_records", "info": {"singularName": "medical-record", "pluralName": "medical-records", "displayName": "Medical Record", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"circuit": {"type": "integer"}, "temperature": {"type": "decimal"}, "blood_pressure": {"type": "decimal"}, "respiratory_rate": {"type": "decimal"}, "height": {"type": "decimal"}, "weight": {"type": "decimal"}, "bmi": {"type": "decimal"}, "spo2": {"type": "decimal"}, "reasons_to_get_hospitalized": {"type": "string"}, "diagnose": {"type": "string"}, "treatment_regimen": {"type": "string"}, "examination": {"type": "string"}, "inquiry": {"type": "string"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "services": {"type": "json"}, "bundle_services": {"type": "json"}, "total": {"type": "decimal"}, "patient": {"type": "relation", "relation": "oneToOne", "target": "api::patient.patient"}, "booking": {"type": "relation", "relation": "oneToOne", "target": "api::booking.booking", "inversedBy": "medical_record"}, "blood_pressure2": {"type": "integer"}, "past_medical_history": {"type": "string"}, "prescription": {"type": "relation", "relation": "oneToOne", "target": "api::prescription.prescription", "mappedBy": "medicalRecord"}, "testResults": {"type": "json"}, "premise": {"type": "string"}, "general_examination": {"type": "string"}, "main_diagnose": {"type": "string"}, "other_diagnose": {"type": "string"}, "membership": {"type": "string"}, "uid": {"type": "string"}, "doctor_in_charge": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "references": {"type": "json"}, "clinique_services": {"type": "json"}, "cc_note": {"type": "string"}, "prescriptions": {"type": "relation", "relation": "oneToMany", "target": "api::prescription.prescription", "mappedBy": "medical_record"}, "noi_khoa": {"type": "text"}, "ngoai_khoa": {"type": "text"}, "san_khoa": {"type": "text"}, "tiem_chung": {"type": "text"}, "di_ung": {"type": "text"}, "thoi_quen": {"type": "text"}, "nguy_co_khac": {"type": "text"}, "van_de_khac": {"type": "text"}, "tien_can_gia_dinh": {"type": "text"}, "tong_quat": {"type": "text"}, "tim_mach": {"type": "text"}, "ho_hap": {"type": "text"}, "tieu_hoa_tiet_nieu": {"type": "text"}, "co_xuong_khop": {"type": "text"}, "than_kinh": {"type": "text"}, "san_phu_khoa": {"type": "text"}, "mat_tai_mui_hong": {"type": "text"}, "co_quan_khac": {"type": "text"}, "cac_thang_diem_can_danh_gia": {"type": "text"}, "dinh_duong": {"type": "text"}, "ket_qua_cls": {"type": "text"}, "chan_doan": {"type": "text"}, "blood_pressure_1": {"type": "integer"}, "blood_pressure2_1": {"type": "integer"}, "status": {"type": "string"}, "cc_in_charge": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "nurse_in_charge": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "cashier_in_charge": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "edit_by": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "session": {"type": "text"}, "nguoi_lien_he_luc_khan_cap": {"type": "text"}, "cac_luong_gia_va_trac_nghiem_da_thuc_hien": {"type": "text"}, "ket_qua_trac_nghiem": {"type": "text"}, "ly_do_den_tham_van": {"type": "text"}, "hanh_vi_quan_sat": {"type": "text"}, "mo_ta_trieu_chung": {"type": "text"}, "thong_tin_nen_tang_boi_canh_lien_quan": {"type": "text"}, "yeu_to_khoi_phat": {"type": "text"}, "yeu_to_bao_ve": {"type": "text"}, "yeu_to_kich_hoat": {"type": "text"}, "yeu_to_duy_tri": {"type": "text"}, "anh_huong_toi_cuoc_song": {"type": "text"}, "cach_giai_quyet_van_de_da_su_dung": {"type": "text"}, "nhu_cau_va_muc_tieu_tham_van": {"type": "text"}, "tom_tat_van_de": {"type": "text"}, "ke_hoach_tham_van": {"type": "json"}, "cac_giay_to_lien_quan": {"type": "json"}, "ghi_chu_cua_cvtl": {"type": "text"}, "is_mental_health_mr": {"type": "boolean"}, "counselor_in_charge": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "chronic_services": {"type": "json"}, "phat_trien": {"type": "text"}, "benh_ly": {"type": "text"}, "is_pediatric_mr": {"type": "boolean"}, "chien_luoc_can_thiep": {"type": "text"}, "theo_doi_sau_phien": {"type": "text"}, "lich_su_nghien_ngap": {"type": "text"}, "is_pediatric_mental_health_mr": {"type": "boolean"}}}