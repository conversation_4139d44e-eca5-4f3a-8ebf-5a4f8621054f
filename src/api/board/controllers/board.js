'use strict';

/**
 * board controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::board.board', ({ strapi }) => ({

    async getBoard(ctx) {
        const { id } = ctx.params;
        const { id: userId } = ctx.state.user;
        var board = await strapi.query("api::board.board").
            findOne({
                where: { id },
                populate: { tasks: {
                    populate: { list: true }
                }, lists: true, }
            });

        const user = await strapi
            .query("plugin::users-permissions.user")
            .findOne({
                where: { id: userId }, populate: {
                role: true,
            }});

        console.log('user', user)

        board.tasks = board.tasks.map(t => {
            t.approvable = t.assigments?.some(a => a.id == userId);
            return t;
        });

        if (user.role?.type != "admin") {
            board.tasks = board.tasks.filter(t => t.approvable);
        }

        return board;
    },
})
);
