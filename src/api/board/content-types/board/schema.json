{"kind": "collectionType", "collectionName": "boards", "info": {"singularName": "board", "pluralName": "boards", "displayName": "Board", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "users": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "lists": {"type": "relation", "relation": "oneToMany", "target": "api::list.list", "mappedBy": "board"}, "tasks": {"type": "relation", "relation": "oneToMany", "target": "api::task.task", "mappedBy": "board"}, "coverPhoto": {"type": "string"}}}