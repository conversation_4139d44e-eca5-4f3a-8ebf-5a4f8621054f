{"kind": "collectionType", "collectionName": "blogs", "info": {"singularName": "blog", "pluralName": "blogs", "displayName": "Blog", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"versions": {"versioned": true}}, "attributes": {"article": {"type": "richtext"}, "test": {"type": "richtext"}, "label": {"type": "string"}, "slug": {"type": "uid", "targetField": "label"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}}}