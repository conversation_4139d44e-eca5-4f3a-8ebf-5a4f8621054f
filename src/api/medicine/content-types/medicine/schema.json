{"kind": "collectionType", "collectionName": "medicines", "info": {"singularName": "medicine", "pluralName": "medicines", "displayName": "Medicine", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"label": {"type": "string"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "desc": {"type": "richtext"}, "products": {"type": "relation", "relation": "manyToMany", "target": "api::product.product", "mappedBy": "medicines"}, "en_products": {"type": "relation", "relation": "manyToMany", "target": "api::product.product", "inversedBy": "en_medicines"}, "en_label": {"type": "string"}, "en_desc": {"type": "richtext"}, "drug": {"type": "relation", "relation": "oneToOne", "target": "api::drug.drug"}}}