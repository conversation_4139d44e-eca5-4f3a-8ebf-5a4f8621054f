'use strict';

/**
 * character controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::character.character', ({ strapi }) => ({

    async updateOne(ctx) {
        const { uid } = ctx.params;
        var svc = await strapi.db.query('api::character.character').findOne({
            where: {
                uid,
            }
        });

        var data = ctx.request.body;
        console.log('ctx.request.body',ctx.request.body)
        data["invited"] = svc.data["invited"];
        data["invite_friend_code"] = svc.data["invite_friend_code"];

        console.log('data', data);

        await strapi
            .query('api::character.character')
            .update({
                where: { uid },
                data: {data}
            });
            return "ok";
    },

    async findOne(ctx) {
        const { uid } = ctx.params;
        var svc = await strapi.db.query('api::character.character').findOne({
            where: {
                uid,
            }
        });

        return {
            blog: svc
        };
    },
    

    async claimReward(ctx) {
        const { host, reward } = ctx.request.body;

        var hU = await strapi.query("api::character.character").
            findOne({
                where: { uid: host },
        });

        hU.data['reward'][reward]['claimable'] = false;
        hU.data['reward'][reward]['claimed'] = true;

        await strapi
            .query('api::character.character')
            .update({
            where: { uid: hU.uid },
            data: {
                data: hU.data
            }
        });

        return 'ok';
    },

    async acceptInvited(ctx) {
        const { guest, host, reward } = ctx.request.body;
        if (host == guest) return "ok";
        var gU = await strapi.query("api::character.character").
            findOne({
                where: { uid: guest },
                populate: { tasks: {
                    populate: { list: true }
                }, lists: true, }
        });

        var hU = await strapi.query("api::character.character").
            findOne({
                where: { uid: host },
                populate: { tasks: {
                    populate: { list: true }
                }, lists: true, }
        });

        let rewardable = false;

        gU.data['invite_friend_code'] = hU.uid;
        if (Array.isArray(hU.data['invited'])) {
            if (hU.data['invited'].indexOf(gU.uid) == -1) {
                hU.data['invited'].push(gU.uid);
                rewardable = true;
            }
        } else {
            hU.data['invited'] = [gU.uid];
            rewardable = true;
        }
        if (Array.isArray(gU.data['invited'])) {
            if (gU.data['invited'].indexOf(hU.uid) == -1) {
                gU.data['invited'].push(hU.uid);
            }
        } else {
            gU.data['invited'] = [hU.uid];
        }

        if (!hU.data['reward']) {
            hU.data['reward'] = {
                "skull_coin": {
                    "claimable": false,
                    "claimed": false,
                },
                "heart_shape_rug": {
                    "claimable": false,
                    "claimed": false,
                    "cnt": 0,
                },
                "car": {
                    "claimable": false,
                    "claimed": false,
                    "cnt": 0,
                }
            };
        }

        if (reward == 'skull_coin' && rewardable) {
            if (reward && (!hU.data['reward'][reward] || (hU.data['reward'][reward] && hU.data['reward'][reward]['claimed'] == false))) {
                hU.data['reward'][reward] = {
                    claimable: true,
                    claimed: false,
                }
            }
        }

        if ((reward == 'heart_shape_rug' || reward == 'car') && rewardable) { 
            hU.data['reward'][reward]["cnt"]++;
            if (hU.data['reward'][reward]["cnt"] == 2 && hU.data['reward'][reward].claimable == false) {
                hU.data['reward'][reward].claimable = true;
                hU.data['reward'][reward].claimed = false;
            }
        }

        await strapi
            .query('api::character.character')
            .update({
            where: { uid: gU.uid },
            data: {
                data: gU.data
            }
        });

        await strapi
            .query('api::character.character')
            .update({
            where: { uid: hU.uid },
            data: {
                data: hU.data
            }
        });

        return 'ok';
    }
    })
)
