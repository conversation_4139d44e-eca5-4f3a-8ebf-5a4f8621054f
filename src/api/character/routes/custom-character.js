module.exports = {
    routes: [
        {
            method: "POST",
            path: "/character/accept-invite",
            handler: "character.acceptInvited",
            config: {
            policies: [],
            prefix: "",
            },
        },
        {
            method: "POST",
            path: "/character/claim-reward",
            handler: "character.claimReward",
            config: {
            policies: [],
            prefix: "",
            },
        },
        {
            method: "POST",
            path: "/character/update-one/:uid",
            handler: "character.updateOne",
            config: {
            policies: [],
            prefix: "",
            },
        },
        {
            method: "GET",
            path: "/character/find-one/:uid",
            handler: "character.findOne",
            config: {
            policies: [],
            prefix: "",
            },
        },
    ]
}