{"kind": "collectionType", "collectionName": "gifts", "info": {"singularName": "gift", "pluralName": "gifts", "displayName": "Gift"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "description": {"type": "string"}, "maxCount": {"type": "integer"}, "currentCount": {"type": "integer"}, "events": {"type": "relation", "relation": "manyToMany", "target": "api::event.event", "mappedBy": "selected_gifts"}}}