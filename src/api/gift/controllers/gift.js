'use strict';

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::gift.gift', ({ strapi }) => ({

  async find(ctx) {
    try {
      const gifts = await strapi.entityService.findMany('api::gift.gift', {
        sort: { createdAt: 'desc' },
      });
      return ctx.send({ success: true, data: gifts }, 200);
    } catch (err) {
      return ctx.send({ success: false, error: err.message }, 500);
    }
  },

}));
