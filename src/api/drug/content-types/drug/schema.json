{"kind": "collectionType", "collectionName": "drugs", "info": {"singularName": "drug", "pluralName": "drugs", "displayName": "Drug", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"code": {"type": "string"}, "label": {"type": "string"}, "type": {"type": "string"}, "ingredient": {"type": "string"}, "stock": {"type": "integer"}, "unit": {"type": "string"}, "label_i": {"type": "string"}, "ingredient_i": {"type": "string"}, "branch": {"type": "string"}, "price": {"type": "integer"}}}