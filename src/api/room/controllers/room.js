'use strict';
const axios = require("axios");

/**
 * room controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::room.room',
    ({ strapi }) => ({

        async login(ctx) {
            const { id } = ctx.state.user;
            // Check if the user exists.
            const user = await strapi.query('plugin::users-permissions.user').findOne({
                where: {
                    id,
                },
            });

            return ctx.send({
                status: 0,
                userUUID: '9ae1809e-a171-4816-b13d-a6a9f2c5c2fd',
            });
        },

        async createWhiteboardRoom(ctx) {
            let response = await axios.post("https://api.netless.link/v5/rooms",
                {
                    "isRecord": false
                },
                {
                    headers: {
                        "token": 'NETLESSSDK_YWs9YmpjY2s3REtPVVRHZzJEViZub25jZT04NGU1NmQzMC1hMjQ3LTExZWUtYTBkMS0xNTkyMjkwZTlkMDcmcm9sZT0wJnNpZz0zZmFkNzg1MDEzOTZmN2ZhMmU4NGRmMGI4NjQyZWU0ODFkMGI0MzY1Mzc0OGNjYjJjMWM4MTM0Y2I4MmMwYmFm',
                        "region": "sg"
                    }
                },
            );

            return {
                status: 0,
                roomUUID: response.data.uuid,
            };
        },

        async generateRoomToken(ctx) {
            const uuid = ctx.request.body.uuid;

            let response = await axios.post("https://shunt-api.netless.link/v5/tokens/rooms/" + uuid,
                {
                    lifespan: 3600000,
                    role: 'admin'
                },
                {
                    headers: {
                        "token": 'NETLESSSDK_YWs9YmpjY2s3REtPVVRHZzJEViZub25jZT04NGU1NmQzMC1hMjQ3LTExZWUtYTBkMS0xNTkyMjkwZTlkMDcmcm9sZT0wJnNpZz0zZmFkNzg1MDEzOTZmN2ZhMmU4NGRmMGI4NjQyZWU0ODFkMGI0MzY1Mzc0OGNjYjJjMWM4MTM0Y2I4MmMwYmFm'
                    }
                },
            );

            return {
                status: 0,
                ownerUUID: '9ae1809e-a171-4816-b13d-a6a9f2c5c2fd',
                roomUUID: uuid,
                whiteboardRoomUUID: uuid,
                whiteboardRoomToken: response.data,
                roomType: 'SmallClass'
            }
        }
    }));
