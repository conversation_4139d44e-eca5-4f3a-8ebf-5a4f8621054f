{"kind": "collectionType", "collectionName": "medical_service_usages", "info": {"singularName": "medical-service-usage", "pluralName": "medical-service-usages", "displayName": "MedicalServiceUsage"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"medical_service": {"type": "relation", "relation": "oneToOne", "target": "api::medical-service.medical-service"}, "patient": {"type": "relation", "relation": "oneToOne", "target": "api::patient.patient"}}}