{"kind": "collectionType", "collectionName": "medical_services", "info": {"singularName": "medical-service", "pluralName": "medical-services", "displayName": "Medical Service", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"label": {"type": "string"}, "code": {"type": "string"}, "host": {"type": "string"}, "price": {"type": "integer"}, "group_service": {"type": "string"}, "service_bundles": {"type": "relation", "relation": "manyToMany", "target": "api::service-bundle.service-bundle", "mappedBy": "medical_services"}, "Locations": {"type": "component", "repeatable": true, "component": "medical-service-setting.medical-service-setting"}, "membership_discount": {"displayName": "Membership Discount", "type": "component", "repeatable": false, "component": "membership-discount.membership-discount"}, "label_i": {"type": "string"}, "tags": {"type": "json"}, "is_mental_health_service": {"type": "boolean"}}}