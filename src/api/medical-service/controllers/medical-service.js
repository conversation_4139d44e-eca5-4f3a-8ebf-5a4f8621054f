'use strict';

/**
 * medical-service controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::medical-service.medical-service', ({ strapi }) => ({

    async getGoldMedicalServices(ctx) {
        const { patientId, membership } = ctx.params;

        let ms = await strapi.query("api::medical-service.medical-service").findMany({
            populate: {
                membership_discount: true,
                Locations: true,
            }
        });

        const patient = await strapi
            .query("api::patient.patient")
            .findOne({ where: { id: patientId }, populate: true });

        // console.log('patient getGoldMedicalServices', patient)
        const freeServices = patient.rem_services ?? [];
        let fs = {};
        freeServices.forEach(f => {
            fs[f.medical_service.data.id] = f.count;
        })

        let results = [];
        await Promise.all(ms.map(async s => {
            if (fs[s.id]) {
                s["original_price"] = s["price"];
                s["discount_note"] = "Dịch vụ còn lại";
                s["discount_percentage"] = 100;
                s["membership_gold"] = true;
                s["rem_service"] = true;
                s["price"] = 0;

                results.push({
                    "id": s.id,
                    "attributes": s
                });
            } else if (s.membership_discount == null || (!patient.membership && !membership)) {
                results.push({
                    "id": s.id,
                    "attributes": s
                });
            } else {
                if (s.membership_discount.platinum_yearly != null && (membership == "platinum" || patient.membership == "platinum")) {
                    var firstDay = new Date();
                    firstDay.setDate(firstDay.getDate() - 365);
                    var lastDay = new Date();
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.platinum_yearly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Thành viên bạch kim";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (s.membership_discount.gold_yearly != null && (membership == "gold" || patient.membership == "gold")) {
                    var firstDay = new Date();
                    firstDay.setDate(firstDay.getDate() - 365);
                    var lastDay = new Date();
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.gold_yearly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Thành viên vàng";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (membership == "family" || patient.membership == "family") {
                    s["original_price"] = s["price"];
                    s["discount_note"] = "Family doctor package";
                    s["discount_percentage"] = 10;
                    s["membership_gold"] = true;
                    s["price"] = s["price"] * 0.9;

                    results.push({
                        "id": s.id,
                        "attributes": s
                    });
                } else if (s.membership_discount.medical_provider_yearly != null && (membership == "medical_provider" || patient.membership == "medical_provider")) {
                    var firstDay = new Date();
                    firstDay.setDate(firstDay.getDate() - 365);
                    var lastDay = new Date();
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.medical_provider_yearly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Medical provider";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (s.membership_discount.infant_yearly != null && (membership == "infant" || patient.membership == "infant")) {
                    var firstDay = new Date();
                    firstDay.setDate(firstDay.getDate() - 365);
                    var lastDay = new Date();
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.infant_yearly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Gói nhũ nhi";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (s.membership_discount.toddler_yearly != null && (membership == "toddler" || patient.membership == "toddler")) {
                    var firstDay = new Date();
                    firstDay.setDate(firstDay.getDate() - 365);
                    var lastDay = new Date();
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.toddler_yearly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Gói nhà trẻ";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (s.membership_discount.preschool_school_age_yearly != null && (membership == "preschool_school_age" || patient.membership == "preschool_school_age")) {
                    var firstDay = new Date();
                    firstDay.setDate(firstDay.getDate() - 365);
                    var lastDay = new Date();
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.preschool_school_age) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Gói học đường";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (s.membership_discount.gold_monthly != null && (membership == "gold" || patient.membership == "gold")) {
                    var date = new Date();
                    var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                    var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.gold_monthly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Thành viên vàng";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (s.membership_discount.platinum_monthly != null && (membership == "platinum" || patient.membership == "platinum")) {
                    var date = new Date();
                    var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                    var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.platinum_monthly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Thành viên bạch kim";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (s.membership_discount.medical_provider_monthly != null && (membership == "medical_provider" || patient.membership == "medical_provider")) {
                    var date = new Date();
                    var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                    var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.medical_provider_monthly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Medical provider";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (s.membership_discount.infant_monthly != null && (membership == "infant" || patient.membership == "infant")) {
                    var date = new Date();
                    var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                    var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.infant_monthly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Gói nhũ nhi";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (s.membership_discount.toddler_monthly != null && (membership == "toddler" || patient.membership == "toddler")) {
                    var date = new Date();
                    var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                    var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.toddler_monthly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Gói nhà trẻ";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else if (s.membership_discount.preschool_school_age_monthly != null && (membership == "preschool_school_age" || patient.membership == "preschool_school_age")) {
                    var date = new Date();
                    var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                    var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                    let msu = await strapi.query("api::medical-service-usage.medical-service-usage").findMany({
                        where: {
                            patient: patientId,
                            medical_service: s.id,
                            publishedAt: {
                                $gte: firstDay,
                                $lte: lastDay,
                            },
                        },
                        populate: {
                            membership_discount: true
                        }
                    });

                    if (msu.length < s.membership_discount.preschool_school_age_monthly) {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Gói học đường";
                        s["discount_percentage"] = 100;
                        s["membership_gold"] = true;
                        s["price"] = 0;

                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    } else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                } else {
                    results.push({
                        "id": s.id,
                        "attributes": s
                    });
                } 
            } 
        }));

        return { data: results };
    },

    async setLabelI(ctx) {
        let services = await strapi.query("api::medical-service.medical-service").findMany({});

        services.forEach(p => {
            strapi
                .query('api::medical-service.medical-service')
                .update({
                    where: { id: p.id },
                    data: {
                        label_i: removeVietnameseTones(p.label ?? "")
                    }
                });
        })

        return [];
    },

    async search(ctx) {
        const { searchTerm } = ctx.params;
        const st = searchTerm.split('|');
        const ors = st.map(s => { return { tags: { $contains: s } } });

        let services = await strapi.query("api::medical-service.medical-service").findMany({
            where: {
                $or: ors,
            },
        });

        services = services.map(s => {
            s["type"] = "service";
            return s;
        });

        let bundleServices = await strapi.query("api::service-bundle.service-bundle").findMany({
            where: {
                $or: ors,
            },
        });

        bundleServices = bundleServices.map(s => {
            s["type"] = "service-bundle";
            return s;
        });

        let products = await strapi.query("api::product.product").findMany({
            where: {
                $or: ors,
            },
            populate: {
                medicines: true,
            }
        });

        products = products.map(s => {
            s["type"] = "product";
            return s;
        });

        return services.concat(bundleServices).concat(products);
    },

    async searchV2(ctx) {
        const { searchTerm, bmi } = ctx.request.body;
        const fBMI = parseFloat(bmi);
        const st = searchTerm.split('|');
        const ors = st.map(s => { return { tags: { $contains: s } } });

        console.log('fBMI', fBMI, bmi)

        let aIDs = []
        if (fBMI >= 23) {
            aIDs = aIDs.concat([62, 94, 107, 108]);
        }

        if (fBMI < 18) {
            aIDs = aIDs.concat([73]);
        }

        aIDs = aIDs.map(i => {
            return {
                id: i,
            }
        })

        let services = await strapi.query("api::medical-service.medical-service").findMany({
            where: {
                $or: ors,
            },
        });

        services = services.map(s => {
            s["type"] = "service";
            return s;
        });

        let bundleServices = await strapi.query("api::service-bundle.service-bundle").findMany({
            where: {
                $or: aIDs.concat(ors),
            },
        });

        bundleServices = bundleServices.map(s => {
            s["type"] = "service-bundle";

            if (s.id == 62) {
                s.tags = [
                    {
                        "searchBy": st[0],
                        "group": "Gói khám"
                    },
                ];
            }
            if (s.id == 94) {
                s.tags = [
                    {
                        "searchBy": st[0],
                        "group": "Gói dinh dưỡng"
                    },
                ];
            }
            if (s.id == 107 || s.id == 108) {
                s.tags = [
                    {
                        "searchBy": st[0],
                        "group": "Gói gene"
                    },
                ];
            }
            if (s.id == 73) {
                s.tags = [
                    {
                        "searchBy": st[0],
                        "group": "Gói dinh dưỡng"
                    },
                ];
            }

            return s;
        });

        let products = await strapi.query("api::product.product").findMany({
            where: {
                $or: ors,
            },
            populate: {
                medicines: true,
            }
        });

        products = products.map(s => {
            s["type"] = "product";
            return s;
        });

        return services.concat(bundleServices).concat(products);
    }
}));


function removeVietnameseTones(str) {
    if (!str) return str;
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
    str = str.replace(/đ/g, "d");
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
    str = str.replace(/Đ/g, "D");
    // Some system encode vietnamese combining accent as individual utf-8 characters
    // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g, " ");
    str = str.trim();
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g, " ");
    return str;
}