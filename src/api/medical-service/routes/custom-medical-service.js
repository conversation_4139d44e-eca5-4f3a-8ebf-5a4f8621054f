module.exports = {
  routes: [
    {
      method: "GET",
      path: "/medical-service/getGoldMedicalServices/:patientId/:membership",
      handler: "medical-service.getGoldMedicalServices",
      config: {
        policies: [],
        prefix: "",
      },
    },
    {
      method: "GET",
      path: "/medical-service/search/:searchTerm",
      handler: "medical-service.search",
      config: {
        policies: [],
        prefix: "",
      },
    },
    {
      method: "POST",
      path: "/medical-service/searchV2",
      handler: "medical-service.searchV2",
      config: {
        policies: [],
        prefix: "",
      },
    },
    {
      method: "GET",
      path: "/medical-service/setLabelI",
      handler: "medical-service.setLabelI",
      config: {
        policies: [],
        prefix: "",
      },
    },
  ]
};
