{"kind": "collectionType", "collectionName": "blogmentals", "info": {"singularName": "blogmental", "pluralName": "blogmentals", "displayName": "Blogmental", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "slug": {"type": "uid", "targetField": "title"}, "category": {"type": "enumeration", "enum": ["daily", "softskills", "selfcare", "stress", "social", "love", "family", "crime"]}, "cover": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "content": {"type": "customField", "options": {"output": "HTML", "preset": "rich"}, "customField": "plugin::ckeditor.CKEditor"}, "author": {"type": "string"}}}