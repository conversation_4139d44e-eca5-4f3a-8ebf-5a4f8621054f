'use strict';


const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::blogmental.blogmental', ({ strapi }) => ({
    async findBySlug(ctx) {
        const { slug } = ctx.params;
        const entity = await strapi.db.query('api::blogmental.blogmental').findOne({
            where: { slug },
            populate: { cover: true },
        });

        if (!entity) {
            return ctx.notFound('Blog post not found');
        }

        const sanitizedEntity = await this.sanitizeOutput(entity, ctx);
        const response = this.transformResponse(sanitizedEntity);

        const coverData = response?.data?.attributes?.cover?.data;
        response.data.attributes.cover = coverData?.attributes?.url
            ? { url: coverData.attributes.url }
            : null;

        const relatedPosts = await strapi.db.query('api::blogmental.blogmental').findMany({
            where: {
                category: entity.category,
                id: { $ne: entity.id },
            },
            limit: 4,
            orderBy: { publishedAt: 'desc' },
            populate: { cover: true },
        });

        const relatedSanitized = await this.sanitizeOutput(relatedPosts, ctx);
        const relatedFormatted = relatedSanitized.map(item => ({
            id: item.id,
            title: item.title,
            slug: item.slug,
            cover: item.cover?.url
                ? { url: item.cover.url }
                : item.cover?.data?.attributes?.url
                    ? { url: item.cover.data.attributes.url }
                    : null,
        }));

        response.data.attributes.related = relatedFormatted;

        return response;
    }
    ,
    async findByCategory(ctx) {
        const { category } = ctx.params;
      
        const entities = await strapi.db.query('api::blogmental.blogmental').findMany({
          where: { category },
          populate: { cover: true },
        });
      
        const sanitizedEntities = await this.sanitizeOutput(entities, ctx);
        const transformed = this.transformResponse(sanitizedEntities);
      
        transformed.data = transformed.data.map(item => {
          const coverData = item.attributes.cover?.data;
          return {
            ...item,
            attributes: {
              ...item.attributes,
              cover: coverData?.attributes?.url
                ? { url: coverData.attributes.url }
                : null,
            },
          };
        });
      
        return transformed;
      }
      
}));
