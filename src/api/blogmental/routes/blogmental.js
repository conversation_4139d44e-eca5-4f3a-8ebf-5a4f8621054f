'use strict';
module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/blogmentals', 
      handler: 'blogmental.find',
      config: {
        policies: [],
        auth: false,
      },
    },
    {
      method: 'GET',
      path: '/blogmentals/:id', 
      handler: 'blogmental.findOne',
      config: {
        policies: [],
        auth: false,
      },
    },
    {
      method: 'POST',
      path: '/blogmentals', 
      handler: 'blogmental.create',
      config: {
        policies: [], 
      },
    },
    {
      method: 'PUT',
      path: '/blogmentals/:id', 
      handler: 'blogmental.update',
      config: {
        policies: [],
      },
    },
    {
      method: 'DELETE',
      path: '/blogmentals/:id',
      handler: 'blogmental.delete',
      config: {
        policies: [], 
      },
    },
    {
      method: 'GET',
      path: '/blogmentals/slug/:slug', 
      handler: 'blogmental.findBySlug',
      config: {
        auth: false,
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/blogmentals/category/:category',
      handler: 'blogmental.findByCategory', 
      config: {
        auth: false, 
        policies: [],
        middlewares: [],
      },
    },
  ],
};
