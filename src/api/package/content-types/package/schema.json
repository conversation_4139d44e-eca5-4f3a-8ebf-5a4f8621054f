{"kind": "collectionType", "collectionName": "packages", "info": {"singularName": "package", "pluralName": "packages", "displayName": "Package", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"label": {"type": "text", "pluginOptions": {}}, "slug": {"type": "uid", "targetField": "label"}, "sub_packages": {"type": "relation", "relation": "oneToMany", "target": "api::sub-package.sub-package", "mappedBy": "package"}, "hero_img": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"], "pluginOptions": {}}, "desc": {"type": "richtext", "pluginOptions": {}}, "en_label": {"pluginOptions": {}, "type": "string"}, "en_desc": {"pluginOptions": {}, "type": "richtext"}, "type": {"type": "enumeration", "enum": ["other", "echomedi_gen", "gen"], "default": "other"}, "label_lv1": {"type": "string"}, "label_lv2": {"type": "string"}, "en_label_lv1": {"type": "string"}, "en_label_lv2": {"type": "string"}}}