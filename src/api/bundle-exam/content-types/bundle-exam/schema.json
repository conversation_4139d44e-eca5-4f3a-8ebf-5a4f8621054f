{"kind": "collectionType", "collectionName": "bundle_exams", "info": {"singularName": "bundle-exam", "pluralName": "bundle-exams", "displayName": "Bundle Exam", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"examinations": {"type": "relation", "relation": "oneToMany", "target": "api::examination.examination", "mappedBy": "bundle_exam"}, "title": {"type": "string"}, "en_title": {"type": "string"}, "slug": {"type": "uid", "targetField": "title"}}}