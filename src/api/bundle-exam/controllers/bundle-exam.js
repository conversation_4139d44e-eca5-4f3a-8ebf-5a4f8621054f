'use strict';

/**
 * bundle-exam controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::bundle-exam.bundle-exam',
    ({ strapi }) => ({
        async findOne(ctx) {
            const { slug } = ctx.params;
            var pkg = await strapi.db.query('api::bundle-exam.bundle-exam').findOne({
                populate: {
                    examinations: {
                        populate: {
                            image: true,
                        }
                    }
                },
                where: {
                    slug
                }
            });

            return {
                package: pkg
            };
        },
    })
);
