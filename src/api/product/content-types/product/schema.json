{"kind": "collectionType", "collectionName": "products", "info": {"singularName": "product", "pluralName": "products", "displayName": "Product", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"label": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"], "pluginOptions": {"i18n": {"localized": true}}}, "desc": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "price": {"type": "integer", "pluginOptions": {"i18n": {"localized": true}}}, "medicines": {"type": "relation", "relation": "manyToMany", "target": "api::medicine.medicine", "inversedBy": "products"}, "slug": {"type": "uid", "targetField": "label"}, "carts": {"type": "relation", "relation": "manyToMany", "target": "api::cart.cart", "mappedBy": "products"}, "en_label": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "en_desc": {"pluginOptions": {"i18n": {"localized": true}}, "type": "richtext"}, "en_medicines": {"type": "relation", "relation": "manyToMany", "target": "api::medicine.medicine", "mappedBy": "en_products"}, "tags": {"pluginOptions": {"i18n": {"localized": true}}, "type": "json"}, "en_slug": {"pluginOptions": {"i18n": {"localized": true}}, "type": "uid", "targetField": "en_label"}}}