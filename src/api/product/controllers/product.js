'use strict';

/**
 * product controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const puppeteer = require('puppeteer');
const fs = require('fs');
const moment = require("moment");
const dayjs = require("dayjs");
const utc = require('dayjs/plugin/utc')
const timezone = require('dayjs/plugin/timezone');

dayjs.extend(utc)
dayjs.extend(timezone);

const body = (s, fontSize) => `
<div>
<div style="width: 100%; display: flex;"><img style="width: 220px; margin-left: 30px; margin-top: 10px;" src="data:image/png;base64,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"/>
<h1 style="position: absolute; font-size: ${fontSize ?? 18}px; top: 30px; left: 270px;">${s}</h1> </div>

</div>
`;


const bodyBenhAn = (s, fontSize, medicalRecordId) => `
<div style="width: 100%; height: 100%; position: relative; margin: 0; padding: 0;">
<div style="width: 100%; display: flex;">
<div style="position: absolute; right: 40px; top: 15px;">
    <p style="margin: 0; padding: 0; font-size: 12px; color: #333;">Mã hồ sơ: ${medicalRecordId}</p>
  </div>
  <img style="width: 200px; margin-left: 30px; margin-top: 10px;" src="data:image/png;base64,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"/>
<img style="height: 70px; margin-left: 80px; margin-top: 20px;" src="data:image/png;base64,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"/>
<h1 style="position: absolute; font-size: ${fontSize ?? 18}px; top: 30px; left: 260px;">${s}</h1> </div>

</div>
`;


const footer = `<style>#header, #footer { padding: 0 !important; }</style>
<img style=" width: 100%; margin-bottom: 0px;" src="data:image/png;base64,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"/>

`;


module.exports = createCoreController('api::product.product',
    ({ strapi }) => ({
        async findOne(ctx) {
            const { slug } = ctx.params;
            var product = await strapi.db.query('api::product.product').findOne({
                populate: {
                    image: true,
                    medicines: {
                        populate: {
                            image: true,
                        }
                    },
                },
                where: {
                    slug
                }
            });

            return {
                product,
            };
        },
        async addProductToCart(ctx) {
            const { user } = ctx.state;
            let cart = await strapi
                .query('api::cart.cart')
                .findOne({
                    where: { users_permissions_user: user.id }, populate: {
                        cart_lines: {
                            populate: {
                                product: true,
                            }
                        }
                    }
                });

            if (!cart) {
                cart = await strapi
                    .query('api::cart.cart')
                    .create({ data: { users_permissions_user: user.id, publishedAt: new Date().toISOString() } });
            }

            await strapi
                .query('api::cart-line.cart-line')
                .create({
                    data: {
                        product: ctx.request.body.product_id,
                        quantity: ctx.request.body.quantity,
                        cart: cart.id,
                        publishedAt: new Date().toISOString()
                    }
                });

            ctx.send({ cart_id: cart.id });
        },
        async addServiceToCart(ctx) {
            const { user } = ctx.state;
            let cart = await strapi
                .query('api::cart.cart')
                .findOne({
                    where: { users_permissions_user: user.id }, populate: {
                        cart_lines: {
                            populate: {
                                product: true,
                            }
                        }
                    }
                });

            if (!cart) {
                cart = await strapi
                    .query('api::cart.cart')
                    .create({ data: { users_permissions_user: user.id, publishedAt: new Date().toISOString() } });
            }

            await strapi
                .query('api::cart-line.cart-line')
                .create({
                    data: {
                        service: ctx.request.body.service_id,
                        quantity: ctx.request.body.quantity,
                        cart: cart.id,
                        publishedAt: new Date().toISOString()
                    }
                });

            ctx.send({ cart_id: cart.id });
        },
        async getCart(ctx) {
            const { user } = ctx.state;
            let cart = await strapi
                .query('api::cart.cart')
                .findOne({
                    where: { users_permissions_user: user.id }
                });

            if (!cart) {
                cart = await strapi
                    .query('api::cart.cart')
                    .create({ data: { users_permissions_user: user.id, publishedAt: new Date().toISOString() } });
            }

            const us = await strapi
                .query('api::cart.cart')
                .findOne({
                    where: { users_permissions_user: user.id },
                    populate: {
                        users_permissions_user: true,
                        cart_lines: {
                            populate: {
                                product: {
                                    populate: {
                                        image: true,
                                    }
                                },
                                service: {
                                    populate: {
                                        image: true,
                                    }
                                }
                            }
                        }
                    }
                });
            return ({ user: us });
        },
        async downloadMedicalRecord(ctx) {
            let mr = await strapi
                .query('api::medical-record.medical-record')
                .findOne({
                    where: { id: ctx.request.body.id },
                    populate: { patient: true, booking: true }
                });

            const patient = mr.patient;

            const browser = await puppeteer.launch({
                headless: 'new',
            })

            // create a new page
            const page = await browser.newPage()

            // set your html as the pages content
            let html = fs.readFileSync(`${__dirname}/medical_record.html`, 'utf8');

            const address = patient?.address
                ? `${patient?.address?.address || ""}, ${patient?.address?.ward?.name || ""}, ${patient?.address?.district?.name || ""
                }, ${patient?.address?.province?.name || ""}`
                : "-";

            html = html.replace("[DATE]", dayjs(mr.createdAt).date());
            html = html.replace("[MONTH]", dayjs(mr.createdAt).month() + 1);
            html = html.replace("[YEAR]", dayjs(mr.createdAt).year());
            html = html.replace("[DAN_TOC]", "Kinh");
            html = html.replace("[AGE]", `${dayjs(patient.birthday).year()} (${(dayjs().year() - dayjs(patient.birthday).year())})`);
            html = html.replace("[CREATED_DAY]", dayjs(mr.createdAt).utc().format("DD/MM/YYYY"));
            html = html.replace("[GIOI_TINH]", patient.gender == "male" ? "Nam" : "Nữ");
            html = html.replace("[FULL_NAME]", patient.full_name);
            html = html.replace("[MACH]", mr.circuit);
            html = html.replace("[NHIET_DO]", mr.temperature ?? "");
            html = html.replace("[HUYET_AP]", mr.blood_pressure ?? "");
            html = html.replace("[HUYET_AP2]", mr.blood_pressure2 ?? "");
            html = html.replace("[NHIP_THO]", mr.respiratory_rate ?? "");
            html = html.replace("[CHIEU_CAO]", mr.height ?? "");
            html = html.replace("[CAN_NANG]", mr.weight ?? "");
            html = html.replace("[BMI]", mr.bmi);
            html = html.replace("[SPO2]", mr.spo2);
            html = html.replace("[TINH_THANH]", ctx.request.body.province);
            html = html.replace("[QUAN_HUYEN]", ctx.request.body.district);
            html = html.replace("[XA_PHUONG]", ctx.request.body.ward);
            html = html.replace("[SDT]", patient.phone);
            html = html.replace("[ADDRESS]", address);
            html = html.replace("[QUOC_GIA]", "VIETNAM");
            html = html.replace("[NGAY_SINH]", dayjs(patient.birthday).utc().format("YYYY-MM-DD"));
            html = html.replace("[QUOC_TICH]", "VIETNAM");
            html = html.replace("[NGHE_NGHIEP]", ctx.request.body.nghe_nghiep ?? "");
            html = html.replace("[DIA_CHI]", address);
            html = html.replace("[EMAIL]", patient.email);
            html = html.replace("[UID]", patient.uid);
            html = html.replace("[LY_DO_VAO_VIEN]", getValueJson(mr.reasons_to_get_hospitalized));
            html = html.replace("[TIEN_CAN]", getValueJson(mr.premise));
            html = html.replace("[BENH_SU]", getValueJson(mr.inquiry));
            html = html.replace("[KHAM_BENH_TONG_QUAT]", getValueJson(mr.general_examination));
            html = html.replace("[KHAM_BENH_CAC_CO_QUAN]", getValueJson(mr.examination));
            html = html.replace("[CHAN_DOAN_BENH_CHINH]", getValueJson(mr.main_diagnose));
            html = html.replace("[CHAN_DOAN_BENH_KEM_THEO]", getValueJson(mr.other_diagnose));
            html = html.replace("[HUONG_DIEU_TRI]", getValueJson(mr.treatment_regimen));

            await page.setContent(html, {
                waitUntil: 'networkidle0'
            })

            var a = await page.createPDFStream(
                {
                    printBackground: true,
                    format: 'A4',
                    margin: {
                        top: "130px",
                        left: "50px",
                        right: "50px",
                        bottom: "200px",
                    },
                    displayHeaderFooter: true,
                    headerTemplate: body("BỆNH ÁN", 25),
                    footerTemplate: footer,
                }
            );

            ctx.send(a);
            a.on('close', async () => {
                try {
                    await page.close();
                    await browser.close();
                } catch (e) {

                }
            });
        },
        async downloadShortenMedicalRecord(ctx) {
            let mr = await strapi
                .query('api::medical-record.medical-record')
                .findOne({
                    where: { id: ctx.request.body.id },
                    populate: { patient: true, booking: true, doctor_in_charge: { populate: true }, }
                });

            const patient = mr.patient;
            const doctorInCharge = mr.doctor_in_charge;


            const browser = await puppeteer.launch({
                headless: true,
            })

            // create a new page
            const page = await browser.newPage()

            // set your html as the pages content
            let html = fs.readFileSync(`${__dirname}/shorten_medical_record.html`, 'utf8');

            const address = parseAddress(patient?.address);

            html = html.replace("[DATE]", dayjs(mr.booking.bookingDate).date());
            html = html.replace("[MONTH]", dayjs(mr.booking.bookingDate).month() + 1);
            html = html.replace("[YEAR]", dayjs(mr.booking.bookingDate).year());
            html = html.replace("[DAN_TOC]", "Kinh");
            html = html.replace("[AGE]", `${dayjs(patient.birthday).year()} (${(dayjs().year() - dayjs(patient.birthday).year())})`);
            html = html.replace("[CREATED_DAY]", dayjs(mr.booking.bookingDate).utc().format("DD/MM/YYYY"));
            html = html.replace("[GIOI_TINH]", patient.gender == "male" ? "Nam" : "Nữ");
            html = html.replace("[FULL_NAME]", patient.full_name);
            html = html.replace("[MACH]", mr.circuit ?? "");
            html = html.replace("[NHIET_DO]", mr.temperature ?? "");
            html = html.replace("[HUYET_AP]", mr.blood_pressure ?? "");
            html = html.replace("[HUYET_AP2]", mr.blood_pressure2 ?? "");
            html = html.replace("[NHIP_THO]", mr.respiratory_rate ?? "");
            html = html.replace("[CHIEU_CAO]", mr.height ?? "");
            html = html.replace("[CAN_NANG]", mr.weight ?? "");
            html = html.replace("[BMI]", mr.bmi ?? "");
            html = html.replace("[SPO2]", mr.spo2 ?? "");
            html = html.replace("[TINH_THANH]", ctx.request.body.province);
            html = html.replace("[QUAN_HUYEN]", ctx.request.body.district);
            html = html.replace("[XA_PHUONG]", ctx.request.body.ward);
            html = html.replace("[SDT]", patient.phone);
            html = html.replace("[ADDRESS]", address);
            html = html.replace("[QUOC_GIA]", "VIETNAM");
            html = html.replace("[NGAY_SINH]", dayjs(patient.birthday).utc().format("YYYY-MM-DD"));
            html = html.replace("[QUOC_TICH]", "VIETNAM");
            html = html.replace("[NGHE_NGHIEP]", ctx.request.body.nghe_nghiep ?? "");
            html = html.replace("[DIA_CHI]", address);
            html = html.replace("[EMAIL]", patient.email);
            html = html.replace("[UID]", patient.uid);
            html = html.replace("[LY_DO_VAO_VIEN]", getValueJsonInline(mr.reasons_to_get_hospitalized));
            html = html.replace("[TIEN_CAN]", getValueJson(mr.premise));
            html = html.replace("[BENH_SU]", getValueJson(mr.inquiry));
            html = html.replace("[KHAM_BENH_TONG_QUAT]", getValueJson(mr.general_examination));
            html = html.replace("[KHAM_BENH_CAC_CO_QUAN]", getValueJson(mr.examination));
            html = html.replace("[CHAN_DOAN_BENH_CHINH]", getValueJsonInline(mr.main_diagnose));
            html = html.replace("[CHAN_DOAN_BENH_KEM_THEO]", getValueJsonInline(mr.other_diagnose));
            html = html.replace("[HUONG_DIEU_TRI]", getValueJson(mr.treatment_regimen));

            if (doctorInCharge?.signature?.url) {
                html = html.replace("[SIGNATURE_IMAGE_URL]", `<img style="position: absolute; right: 0; width: 170px;" src="https://api.echomedi.com${doctorInCharge?.signature?.url ?? ''}"/>`);
            } else {
                html = html.replace("[SIGNATURE_IMAGE_URL]", '<div style="height: 80px; width: 170px;"></div>');
            }
            if (doctorInCharge?.patient?.full_name) {
                html = html.replace("[DOCTOR_NAME]", `<pre style="text-align: right; margin-top: 15px; margin-top: 100px;">${doctorInCharge?.patient?.full_name}</pre>`);
            } else {
                html = html.replace("[DOCTOR_NAME]", '');
            }

            await page.setContent(html, {
                waitUntil: 'networkidle0'
            })

            var a = await page.createPDFStream(
                {
                    printBackground: true,
                    format: 'A4',
                    margin: {
                        top: "120px",
                        left: "70px",
                        right: "70px",
                        bottom: "200px",
                    },
                    displayHeaderFooter: true,
                    headerTemplate: body("TÓM TẮT BỆNH ÁN"),
                    footerTemplate: footer,
                }
            );

            ctx.send(a);
            a.on('close', async () => {
                try {
                    await page.close();
                    await browser.close();
                } catch (e) {

                }
            });
        },
        async downloadShortenMedicalRecordV2(ctx) {
            let mr = await strapi
                .query('api::medical-record.medical-record')
                .findOne({
                    where: { id: ctx.request.body.id },
                    populate: { patient: true, booking: true, doctor_in_charge: { populate: true }, }
                });

            const patient = mr.patient;
            const doctorInCharge = mr.doctor_in_charge;


            const browser = await puppeteer.launch({
                headless: true
            })
            const page = await browser.newPage()
            let html = fs.readFileSync(`${__dirname}/shorten_medical_record_v2.html`, 'utf8');
            const address = parseAddress(patient?.address);
            html = html.replaceAll("[DATE]", dayjs(mr.booking.bookingDate).date());
            html = html.replaceAll("[MONTH]", dayjs(mr.booking.bookingDate).month() + 1);
            html = html.replaceAll("[YEAR]", dayjs(mr.booking.bookingDate).year());
            html = html.replaceAll("[DAN_TOC]", "Kinh");
            html = html.replaceAll("[AGE]", patient.birthday ? `${dayjs(patient.birthday).year()} (${(dayjs().year() - dayjs(patient.birthday).year())})` : '');
            html = html.replaceAll("[CREATED_DAY]", dayjs(mr.booking.bookingDate).utc().format("DD/MM/YYYY"));
            html = html.replaceAll("[GIOI_TINH]", patient.gender == "male" ? "Nam" : "Nữ");
            html = html.replaceAll("[FULL_NAME]", patient.full_name?.toUpperCase());
            html = html.replaceAll("[MACH]", mr.circuit ?? "");
            html = html.replaceAll("[NHIET_DO]", mr.temperature ?? "");
            if (mr.blood_pressure_1) {
                html = html.replaceAll("[HUYET_AP_LAN_1]", `lần 1: ${mr.blood_pressure ?? ''}/${mr.blood_pressure2 ?? ''}`);
                html = html.replaceAll("[HUYET_AP_LAN_2]", `Huyết áp lần 2: ${mr.blood_pressure_1 ?? ''}/${mr.blood_pressure2_1 ?? ''} mmHg`);
            } else {
                html = html.replaceAll("[HUYET_AP_LAN_1]", `: ${mr.blood_pressure ?? ''}/${mr.blood_pressure2 ?? ''}`);
                html = html.replaceAll("[HUYET_AP_LAN_2]", ``);
            }
            // html = html.replaceAll("[HUYET_AP_LAN_1]", mr.blood_pressure ?? "");
            html = html.replaceAll("[HUYET_AP]", mr.blood_pressure ?? "");
            html = html.replaceAll("[HUYET_AP2]", mr.blood_pressure2 ?? "");
            html = html.replaceAll("[HUYET_AP_1]", mr.blood_pressure_1 ?? "");
            html = html.replaceAll("[HUYET_AP2_1]", mr.blood_pressure2_1 ?? "");
            html = html.replaceAll("[NHIP_THO]", mr.respiratory_rate ?? "");
            html = html.replaceAll("[CHIEU_CAO]", mr.height ?? "");
            html = html.replaceAll("[CAN_NANG]", mr.weight ?? "");
            html = html.replaceAll("[BMI]", mr.bmi ?? "");
            html = html.replaceAll("[SPO2]", mr.spo2 ?? "");
            html = html.replaceAll("[TINH_THANH]", ctx.request.body.province);
            html = html.replaceAll("[QUAN_HUYEN]", ctx.request.body.district);
            html = html.replaceAll("[XA_PHUONG]", ctx.request.body.ward);
            html = html.replaceAll("[SDT]", patient.phone);
            html = html.replaceAll("[ADDRESS]", address);
            html = html.replaceAll("[QUOC_GIA]", "VIETNAM");
            html = html.replaceAll("[NGAY_SINH]", dayjs(patient.birthday).utc().format("YYYY-MM-DD"));
            html = html.replaceAll("[QUOC_TICH]", "VIETNAM");
            html = html.replaceAll("[NGHE_NGHIEP]", ctx.request.body.nghe_nghiep ?? "");
            html = html.replaceAll("[DIA_CHI]", address);
            html = html.replaceAll("[EMAIL]", patient.email);
            html = html.replaceAll("[UID]", patient.uid);
            html = html.replaceAll("[LY_DO_VAO_VIEN]", mr.reasons_to_get_hospitalized ?? "");
            html = html.replaceAll("[TIEN_CAN]", getValueJson(mr.premise));
            html = html.replaceAll("[BENH_SU]", getValueJson(mr.inquiry));
            html = html.replaceAll("[KHAM_BENH_TONG_QUAT]", getValueJson(mr.general_examination));
            html = html.replaceAll("[KHAM_BENH_CAC_CO_QUAN]", getValueJson(mr.examination));
            html = html.replaceAll("[CHAN_DOAN_BENH_CHINH]", getValueJsonInline(mr.main_diagnose));
            html = html.replaceAll("[CHAN_DOAN_BENH_KEM_THEO]", getValueJsonInline(mr.other_diagnose));
            html = html.replaceAll("[HUONG_DIEU_TRI]", getValueJson(mr.treatment_regimen));
            html = html.replaceAll("[NOI_KHOA]", mr.noi_khoa ?? '');
            html = html.replaceAll("[NGOAI_KHOA]", mr.ngoai_khoa ?? '');
            html = html.replaceAll("[SAN_KHOA]", mr.san_khoa ?? '');
            html = html.replaceAll("[SAN_PHU_KHOA]", mr.san_phu_khoa ?? '');
            html = html.replaceAll("[TIEM_CHUNG]", mr.tiem_chung ?? '');
            html = html.replaceAll("[DI_UNG]", mr.di_ung ?? '');
            html = html.replaceAll("[THOI_QUEN]", mr.thoi_quen ?? '');
            html = html.replaceAll("[NGUY_CO_KHAC]", mr.nguy_co_khac ?? '');
            html = html.replaceAll("[VAN_DE_KHAC]", mr.van_de_khac ?? '');
            html = html.replaceAll("[TIM_MACH]", mr.tim_mach ?? '');
            html = html.replaceAll("[HO_HAP]", mr.ho_hap ?? '');
            html = html.replaceAll("[TIEU_HOA_TIET_NIEU]", mr.tieu_hoa_tiet_nieu ?? '');
            html = html.replaceAll("[CO_XUONG_KHOP]", mr.co_xuong_khop ?? '');
            html = html.replaceAll("[THAN_KINH]", mr.than_kinh ?? '');
            html = html.replaceAll("[MAT_TAI_MUI_HONG]", mr.mat_tai_mui_hong ?? '');
            html = html.replaceAll("[CO_QUAN_KHAC]", mr.co_quan_khac ?? '');
            html = html.replaceAll("[TIEN_CAN_GIA_DINH]", mr.tien_can_gia_dinh ?? '');
            html = html.replaceAll("[TONG_QUAT]", mr.tong_quat ?? '');

            html = html.replaceAll("[KET_QUA_CAN_LAM_SANG]", getValueJson(mr.ket_qua_cls));
            html = html.replaceAll("[CHAN_DOAN]", getValueJson(mr.chan_doan));

            if (doctorInCharge?.signature?.url) {
                html = html.replace("[SIGNATURE_IMAGE_URL]", `<img style="right: -20px; width: 200px;" src="https://api.echomedi.com${doctorInCharge?.signature?.url ?? ''}"/>`);
            } else {
                html = html.replace("[SIGNATURE_IMAGE_URL]", '<div style="height: 80px; width: 170px;"></div>');
            }
            if (doctorInCharge?.patient?.full_name) {
                html = html.replace("[DOCTOR_NAME]", `<div style="display: flex;"><prev style="text-align: center; width: 100%; ">${doctorInCharge?.patient?.full_name}</prev></div>`);
            } else {
                html = html.replace("[DOCTOR_NAME]", '');
            }

            html.replaceAll("-->", "->");

            await page.setContent(html, {
                waitUntil: 'networkidle0'
            })

            var a = await page.createPDFStream(
                {
                    printBackground: true,
                    format: 'A4',
                    margin: {
                        top: "120px",
                        left: "65px",
                        right: "65px",
                        bottom: "130px",
                    },
                    displayHeaderFooter: true,
                    headerTemplate: bodyBenhAn("", 25, mr.uid),
                    footerTemplate: footer,
                }
            );

            ctx.send(a);
            a.on('close', async () => {
                try {
                    await page.close();
                    await browser.close();
                } catch (e) {

                }
            });
        },
        async downloadShortenPediatricMedicalRecordV2(ctx) {
            let mr = await strapi
                .query('api::medical-record.medical-record')
                .findOne({
                    where: { id: ctx.request.body.id },
                    populate: { patient: { populate: { relationships: { populate: { patient: true }}}}, booking: true, doctor_in_charge: { populate: true }, }
                });

            const patient = mr.patient;
            const doctorInCharge = mr.doctor_in_charge;

            const rel = patient.relationships ? patient.relationships[0] : null;


            const browser = await puppeteer.launch({
                headless: true
            })
            const page = await browser.newPage()
            let html = fs.readFileSync(`${__dirname}/shorten_pediatric_medical_record_v2.html`, 'utf8');
            const address = parseAddress(patient?.address);
            html = html.replaceAll("[DATE]", dayjs(mr.booking.bookingDate).date());
            html = html.replaceAll("[TEN_NGUOI_THAN]", rel ? (rel?.label + ": " + rel?.patient.full_name) : '');
            html = html.replaceAll("[SDT_NGUOI_THAN]", rel ? (rel?.patient.phone) : '');
            html = html.replaceAll("[MONTH]", dayjs(mr.booking.bookingDate).month() + 1);
            html = html.replaceAll("[YEAR]", dayjs(mr.booking.bookingDate).year());
            html = html.replaceAll("[DAN_TOC]", "Kinh");
            // html = html.replaceAll("[AGE]", patient.birthday ? `${dayjs(patient.birthday).year()} (${(dayjs().year() - dayjs(patient.birthday).year())})` : '');
            html = html.replaceAll("[AGE]", patient.birthday ? `${ monthDiff(dayjs(patient.birthday), dayjs())}` : '');
            html = html.replaceAll("[CREATED_DAY]", dayjs(mr.booking.bookingDate).utc().format("DD/MM/YYYY"));
            html = html.replaceAll("[GIOI_TINH]", patient.gender == "male" ? "Nam" : "Nữ");
            html = html.replaceAll("[FULL_NAME]", patient.full_name?.toUpperCase());
            html = html.replaceAll("[MACH]", mr.circuit ?? "");
            html = html.replaceAll("[NHIET_DO]", mr.temperature ?? "");
            if (mr.blood_pressure_1) {
                html = html.replaceAll("[HUYET_AP_LAN_1]", `lần 1: ${mr.blood_pressure ?? ''}/${mr.blood_pressure2 ?? ''}`);
                html = html.replaceAll("[HUYET_AP_LAN_2]", `Huyết áp lần 2: ${mr.blood_pressure_1 ?? ''}/${mr.blood_pressure2_1 ?? ''} mmHg`);
            } else {
                html = html.replaceAll("[HUYET_AP_LAN_1]", `: ${mr.blood_pressure ?? ''}/${mr.blood_pressure2 ?? ''}`);
                html = html.replaceAll("[HUYET_AP_LAN_2]", ``);
            }
            // html = html.replaceAll("[HUYET_AP_LAN_1]", mr.blood_pressure ?? "");
            html = html.replaceAll("[HUYET_AP]", mr.blood_pressure ?? "");
            html = html.replaceAll("[HUYET_AP2]", mr.blood_pressure2 ?? "");
            html = html.replaceAll("[HUYET_AP_1]", mr.blood_pressure_1 ?? "");
            html = html.replaceAll("[HUYET_AP2_1]", mr.blood_pressure2_1 ?? "");
            html = html.replaceAll("[NHIP_THO]", mr.respiratory_rate ?? "");
            html = html.replaceAll("[CHIEU_CAO]", mr.height ?? "");
            html = html.replaceAll("[CAN_NANG]", mr.weight ?? "");
            html = html.replaceAll("[BMI]", mr.bmi ?? "");
            html = html.replaceAll("[SPO2]", mr.spo2 ?? "");
            html = html.replaceAll("[TINH_THANH]", ctx.request.body.province);
            html = html.replaceAll("[QUAN_HUYEN]", ctx.request.body.district);
            html = html.replaceAll("[XA_PHUONG]", ctx.request.body.ward);
            html = html.replaceAll("[SDT]", patient.phone);
            html = html.replaceAll("[ADDRESS]", address);
            html = html.replaceAll("[QUOC_GIA]", "VIETNAM");
            html = html.replaceAll("[NGAY_SINH]", dayjs(patient.birthday).utc().format("YYYY-MM-DD"));
            html = html.replaceAll("[QUOC_TICH]", "VIETNAM");
            html = html.replaceAll("[NGHE_NGHIEP]", ctx.request.body.nghe_nghiep ?? "");
            html = html.replaceAll("[DIA_CHI]", address);
            html = html.replaceAll("[EMAIL]", patient.email);
            html = html.replaceAll("[UID]", patient.uid);
            html = html.replaceAll("[LY_DO_VAO_VIEN]", mr.reasons_to_get_hospitalized ?? "");
            html = html.replaceAll("[TIEN_CAN]", getValueJson(mr.premise));
            html = html.replaceAll("[BENH_SU]", getValueJson(mr.inquiry));
            html = html.replaceAll("[KHAM_BENH_TONG_QUAT]", getValueJson(mr.general_examination));
            html = html.replaceAll("[KHAM_BENH_CAC_CO_QUAN]", getValueJson(mr.examination));
            html = html.replaceAll("[CHAN_DOAN_BENH_CHINH]", getValueJsonInline(mr.main_diagnose));
            html = html.replaceAll("[CHAN_DOAN_BENH_KEM_THEO]", getValueJsonInline(mr.other_diagnose));
            html = html.replaceAll("[HUONG_DIEU_TRI]", getValueJson(mr.treatment_regimen));
            html = html.replaceAll("[NOI_KHOA]", mr.noi_khoa ?? '');
            html = html.replaceAll("[NGOAI_KHOA]", mr.ngoai_khoa ?? '');
            html = html.replaceAll("[SAN_KHOA]", mr.san_khoa ?? '');
            html = html.replaceAll("[PHAT_TRIEN]", mr.phat_trien ?? '');
            html = html.replaceAll("[BENH_LY]", mr.benh_ly ?? '');
            html = html.replaceAll("[DINH_DUONG]", mr.dinh_duong ?? '');
            html = html.replaceAll("[SAN_PHU_KHOA]", mr.san_phu_khoa ?? '');
            html = html.replaceAll("[TIEM_CHUNG]", mr.tiem_chung ?? '');
            html = html.replaceAll("[DI_UNG]", mr.di_ung ?? '');
            html = html.replaceAll("[THOI_QUEN]", mr.thoi_quen ?? '');
            html = html.replaceAll("[NGUY_CO_KHAC]", mr.nguy_co_khac ?? '');
            html = html.replaceAll("[VAN_DE_KHAC]", mr.van_de_khac ?? '');
            html = html.replaceAll("[TIM_MACH]", mr.tim_mach ?? '');
            html = html.replaceAll("[HO_HAP]", mr.ho_hap ?? '');
            html = html.replaceAll("[TIEU_HOA_TIET_NIEU]", mr.tieu_hoa_tiet_nieu ?? '');
            html = html.replaceAll("[CO_XUONG_KHOP]", mr.co_xuong_khop ?? '');
            html = html.replaceAll("[THAN_KINH]", mr.than_kinh ?? '');
            html = html.replaceAll("[MAT_TAI_MUI_HONG]", mr.mat_tai_mui_hong ?? '');
            html = html.replaceAll("[CO_QUAN_KHAC]", mr.co_quan_khac ?? '');
            html = html.replaceAll("[TIEN_CAN_GIA_DINH]", mr.tien_can_gia_dinh ?? '');
            html = html.replaceAll("[TONG_QUAT]", mr.tong_quat ?? '');

            html = html.replaceAll("[KET_QUA_CAN_LAM_SANG]", getValueJson(mr.ket_qua_cls));
            html = html.replaceAll("[CHAN_DOAN]", getValueJson(mr.chan_doan));

            if (doctorInCharge?.signature?.url) {
                html = html.replace("[SIGNATURE_IMAGE_URL]", `<img style="right: -20px; width: 200px;" src="https://api.echomedi.com${doctorInCharge?.signature?.url ?? ''}"/>`);
            } else {
                html = html.replace("[SIGNATURE_IMAGE_URL]", '<div style="height: 80px; width: 170px;"></div>');
            }
            if (doctorInCharge?.patient?.full_name) {
                html = html.replace("[DOCTOR_NAME]", `<div style="display: flex;"><prev style="text-align: center; width: 100%; ">${doctorInCharge?.patient?.full_name}</prev></div>`);
            } else {
                html = html.replace("[DOCTOR_NAME]", '');
            }

            html.replaceAll("-->", "->");

            await page.setContent(html, {
                waitUntil: 'networkidle0'
            })

            var a = await page.createPDFStream(
                {
                    printBackground: true,
                    format: 'A4',
                    margin: {
                        top: "120px",
                        left: "65px",
                        right: "65px",
                        bottom: "130px",
                    },
                    displayHeaderFooter: true,
                    headerTemplate: bodyBenhAn("", 25, mr.uid),
                    footerTemplate: footer,
                }
            );

            ctx.send(a);
            a.on('close', async () => {
                try {
                    await page.close();
                    await browser.close();
                } catch (e) {

                }
            });
        },
        async generatePDF(ctx) {
            const browser = await puppeteer.launch({
                headless: false,
            })

            // create a new page
            const page = await browser.newPage()

            // set your html as the pages content
            let html = fs.readFileSync(`${__dirname}/medical_record.html`, 'utf8');

            html = html.replace("[DAN_TOC]", ctx.request.body.dan_toc);
            html = html.replace("[GIOI_TINH]", ctx.request.body.gender == "male" ? "Nam" : "Nữ");
            html = html.replace("[FULL_NAME]", ctx.request.body.full_name);
            html = html.replace("[MACH]", ctx.request.body.circuit);
            html = html.replace("[NHIET_DO]", ctx.request.body.temperature);
            html = html.replace("[HUYET_AP]", ctx.request.body.blood_pressure);
            html = html.replace("[HUYET_AP2]", ctx.request.body.blood_pressure);
            html = html.replace("[NHIP_THO]", ctx.request.body.respiratory_rate);
            html = html.replace("[CHIEU_CAO]", ctx.request.body.height);
            html = html.replace("[CAN_NANG]", ctx.request.body.weight ?? "");
            html = html.replace("[BMI]", ctx.request.body.bmi);
            html = html.replace("[SPO2]", ctx.request.body.spo2);
            html = html.replace("[TINH_THANH]", ctx.request.body.province);
            html = html.replace("[QUAN_HUYEN]", ctx.request.body.district);
            html = html.replace("[XA_PHUONG]", ctx.request.body.ward);
            html = html.replace("[SDT]", ctx.request.body.phone);
            html = html.replace("[ADDRESS]", ctx.request.body.address);
            html = html.replace("[QUOC_GIA]", ctx.request.body.quoc_gia);
            html = html.replace("[NGAY_SINH]", dayjs(ctx.request.body.ngay_sinh).utc().format("YYYY-MM-DD"));
            html = html.replace("[QUOC_TICH]", ctx.request.body.quoc_tich);
            html = html.replace("[NGHE_NGHIEP]", ctx.request.body.nghe_nghiep);
            html = html.replace("[DIA_CHI]", ctx.request.body.address);
            html = html.replace("[EMAIL]", ctx.request.body.email);
            html = html.replace("[UID]", ctx.request.body.uid || "");
            html = html.replace("[LY_DO_VAO_VIEN]", getValueJson(ctx.request.body.ly_do_vao_vien));
            html = html.replace("[HOI_BENH]", getValueJson(ctx.request.body.hoi_benh));
            html = html.replace("[KHAM_BENH]", getValueJson(ctx.request.body.kham_benh));
            html = html.replace("[CHAN_DOAN]", getValueJson(ctx.request.body.chan_doan));
            html = html.replace("[HUONG_DIEU_TRI]", getValueJson(ctx.request.body.huong_dieu_tri));


            await page.setContent(html, {
                waitUntil: 'networkidle0'
            })

            var a = await page.createPDFStream({ printBackground: true, width: "1118px", height: "1685px" });

            ctx.send(a);
            a.on('close', async () => {
                try {
                    await page.close();
                    await browser.close();
                } catch (e) {

                }
            });
        },

        async generatePhieuCLS(ctx) {
            const browser = await puppeteer.launch({
                headless: true
            });

            let order = await strapi
                .query('api::medical-record.medical-record')
                .findOne({ where: { id: ctx.request.body.id } });

            let services = JSON.parse(order.services);
            // let bundle_services = JSON.parse(order.bundle_services);

            // create a new page
            const page = await browser.newPage();
            // set your html as the pages content
            let html = fs.readFileSync(`${__dirname}/phieu_cls.html`, 'utf8');

            // html = html.replace("[DAN_TOC]", ctx.request.body.dan_toc);
            // html = html.replace("[FULL_NAME]", ctx.request.body.full_name);
            // html = html.replace("[MACH]", ctx.request.body.mach);
            // html = html.replace("[NHIET_DO]", ctx.request.body.nhiet_do);
            // html = html.replace("[HUYET_AP]", ctx.request.body.huyet_ap);
            // html = html.replace("[NHIP_THO]", ctx.request.body.nhip_tho);
            // html = html.replace("[CHIEU_CAO]", ctx.request.body.chieu_cao);
            // html = html.replace("[CAN_NANG]", ctx.request.body.can_nang);
            // html = html.replace("[BMI]", ctx.request.body.bmi);
            // html = html.replace("[SPO2]", ctx.request.body.spo2);
            await page.setContent(html, {
                waitUntil: 'domcontentloaded'
            });


            page.on('console', async (msg) => {
                const msgArgs = msg.args();
                for (let i = 0; i < msgArgs.length; ++i) {
                }
            });


            await page.evaluate((services, bs) => {
                function numberWithCommas(x) {
                    return x?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                }


                let total = 0;
                let cnt = 0;
                let tableContainer = document.getElementById('table-container');
                let bundle_services = JSON.parse(bs);
                var a = document.getElementById('table');
                services.forEach(s => {
                    cnt++;
                    var tr = document.createElement("tr");
                    var td1 = document.createElement("td");
                    if (cnt % 50 == 0) {
                        td1.className = "p-150";
                    }
                    td1.innerHTML = s.attributes.label;
                    var td2 = document.createElement("td");
                    td2.innerHTML = s.attributes.group_service;
                    var td3 = document.createElement("td");
                    td3.classList.add("price");
                    td3.innerHTML = numberWithCommas(s.attributes.price);
                    total += s.attributes.price;
                    tr.append(td1);
                    tr.append(td2);
                    tr.append(td3);
                    if (cnt % 50 == 0) {
                        tr.className = "page-break-after-el";
                    }
                    a.append(tr);
                });
                bundle_services.forEach(b => {
                    cnt++;
                    var tr = document.createElement("tr");
                    var td1 = document.createElement("td");
                    td1.innerHTML = b.attributes.label;
                    td1.className = "bold";
                    if (cnt % 50 == 0) {
                        td1.classList.add("p-150");
                    }
                    tr.append(td1);
                    var td2 = document.createElement("td");
                    td2.innerHTML = "";
                    tr.append(td2);
                    var td3 = document.createElement("td");
                    td3.innerHTML = numberWithCommas(b.attributes.price);
                    td3.classList.add('price');
                    total += b.attributes.price;
                    tr.append(td3);
                    if (cnt % 50 == 0) tr.className = "page-break-after-el";
                    a.append(tr);

                    var medical_services = b.attributes.medical_services;

                    medical_services.data.forEach(ms => {
                        cnt++;
                        var tr = document.createElement("tr");
                        var td1 = document.createElement("td");
                        td1.innerHTML = ms.attributes.label;
                        if (cnt % 50 == 0) {
                            td1.classList.add("p-150");
                        }
                        var td2 = document.createElement("td");
                        td2.innerHTML = ms.attributes.group_service;
                        var td3 = document.createElement("td");
                        td3.innerHTML = "";
                        tr.append(td1);
                        tr.append(td2);
                        tr.append(td3);
                        if (cnt % 50 == 0) tr.className = "page-break-after-el";
                        a.append(tr);
                    })
                })
                var tr = document.createElement("tr");
                var td1 = document.createElement("td");
                td1.innerHTML = "";
                tr.append(td1);
                var td2 = document.createElement("td");
                td2.innerHTML = "";
                tr.append(td2);
                var td3 = document.createElement("td");
                td3.innerHTML = numberWithCommas(total);
                td3.className = "bold price";
                tr.append(td3);
                cnt++;
                if (cnt % 50 == 0) tr.className = "page-break-after-el";
                a.append(tr);
            }, services, order.bundle_services.toString());

            var a = await page.createPDFStream({ printBackground: true, width: "1118px", height: "1685px", preferCSSPageSize: true });

            ctx.send(a);
            a.on('close', async () => {
                try {
                    await page.close();
                    await browser.close();
                } catch (e) {

                }
            });
        },
        async generatePhieuChiDinh(ctx) {
            const browser = await puppeteer.launch({
                headless: true
            });

            let order = await strapi
                .query('api::medical-record.medical-record')
                .findOne({ where: { id: ctx.request.body.id }, populate: { booking: true, patient: true, doctor_in_charge: { populate: true } } });

            const patient = order.patient;
            const doctorInCharge = order.doctor_in_charge;

            let services = typeof order.services == 'string' ? JSON.parse(order.services) : order.services;
            // create a new page
            const page = await browser.newPage();
            // set your html as the pages content
            let html = fs.readFileSync(`${__dirname}/phieu_cls.html`, 'utf8');

            const address =
                patient.address?.address + ", " +
                patient.address?.ward?.name + ", " +
                patient.address?.district?.name + ", " +
                patient.address?.province?.name;

            html = html.replace("[FULL_NAME]", patient.full_name.toUpperCase());
            html = html.replace("[DIA_CHI]", address);
            html = html.replace("[SDT]", patient.phone);
            html = html.replace("[AGE]", `${dayjs(patient.birthday).year()} (${(dayjs().year() - dayjs(patient.birthday).year())})`);
            html = html.replace("[GIOI_TINH]", patient.gender == "male" ? "Nam" : "Nữ");
            html = html.replace("[EMAIL]", patient.email);
            html = html.replace("[UID]", patient.uid);
            // html = html.replace("[CREATED_DAY]", dayjs(order.createdAt).utc().format("DD/MM/YYYY"));
            html = html.replace("[CREATED_DAY]", dayjs(order.booking.bookingDate).utc().format("DD/MM/YYYY"));
            html = html.replace("[MAIN_DIAGNOSE]", getValueJsonInlineSeparator(order.main_diagnose));
            html = html.replace("[OTHER_DIAGNOSE]", getValueJsonInlineSeparator(order.other_diagnose));
            html = html.replace("[CHAN_DOAN]", order.chan_doan ?? '');

            if (doctorInCharge?.signature?.url) {
                html = html.replaceAll("[SIGNATURE_IMAGE_URL]", `<img style="display: none; right: 0; width: 170px;" src="https://api.echomedi.com${doctorInCharge?.signature?.url ?? ''}"/>`);
            } else {
                html = html.replaceAll("[SIGNATURE_IMAGE_URL]", '');
            }

            let h = `<div style="">
            <prev>Họ và tên: [FULL_NAME]<span style="position: absolute; left: 350px">Tuổi: [AGE]</span><span style="position: absolute; left: 500px">Giới tính: [GIOI_TINH]</span></prev>
            <div>
                    <prev>Địa chỉ: [ADDRESS]</prev>
            </div>
            <div>
                    <prev>Số điện thoại: [SDT] <span style="position: absolute; left: 350px">Email:
                                    [EMAIL]</span></prev>
            </div>
            <div>
                    <prev>Ngày đến khám: [CREATED_DAY]</prev>
            </div>
            <prev>Chẩn đoán: [CHAN_DOAN]</prev>
    </div>`;
            h = h.replace("[FULL_NAME]", patient.full_name?.toUpperCase());
            h = h.replace("[ADDRESS]", address);
            h = h.replace("[SDT]", patient.phone);
            h = h.replace("[AGE]", `${dayjs(patient.birthday).year()} (${(dayjs().year() - dayjs(patient.birthday).year())})`);
            h = h.replace("[GIOI_TINH]", patient.gender == "male" ? "Nam" : "Nữ");
            h = h.replace("[EMAIL]", patient.email);
            h = h.replace("[UID]", patient.uid);
            // h = h.replace("[CREATED_DAY]", dayjs(order.createdAt).utc().format("DD/MM/YYYY"));
            h = h.replace("[CREATED_DAY]", dayjs(order.booking.bookingDate).utc().format("DD/MM/YYYY"));
            h = h.replace("[MAIN_DIAGNOSE]", getValueJsonInlineSeparator(order.main_diagnose));
            h = h.replace("[OTHER_DIAGNOSE]", getValueJsonInlineSeparator(order.other_diagnose));
            h = h.replace("[CHAN_DOAN]", order.chan_doan ?? '');

            await page.setContent(html, {
                waitUntil: 'networkidle0'
            });

            let bundle_services = typeof order.bundle_services == 'string' ? JSON.parse(order.bundle_services) : order.bundle_services;
            bundle_services.forEach(b => {
                if (Array.isArray(b.attributes.medical_services.data)) {
                    b.attributes.medical_services.data.forEach(ms => {
                        ms.attributes.combo = b.attributes.label;
                        services.push(ms);
                    })
                }

                if (Array.isArray(b.attributes.medical_services)) {
                    b.attributes.medical_services.forEach(ms => {
                        // ms.attributes.combo = b.attributes.label;
                        ms.attributes = { ...ms };
                        services.push(ms);
                    })
                }
            })

            function compareFn(a, b) {
                if (a.attributes.group_service < b.attributes.group_service) {
                    return -1;
                } else if (a.attributes.group_service > b.attributes.group_service) {
                    return 1;
                }
                // a must be equal to b
                return 0;
            }

            services.sort(compareFn);

            const groupByCategory = services.reduce((group, product) => {
                const { host } = product.attributes;
                let gHost;
                if (host == "Diag" || host == "Echo Medi") {
                    gHost = 1;
                } else {
                    gHost = 2;
                }
                group[gHost] = group[gHost] ?? [];
                group[gHost].push(product);
                return group;
            }, {});



            page.on('console', async (msg) => {
                const msgArgs = msg.args();
                for (let i = 0; i < msgArgs.length; ++i) {
                }
            });

            await page.evaluate((groupByCategory, h, date, month, year, doctorInCharge) => {
                let tableContainer = document.getElementById('table-container');
                var a = document.getElementById('table');
                let cnt = 1;
                Object.entries(groupByCategory).forEach(entry => {
                    const [key, value] = entry;

                    value.forEach(s => {
                        var tr = document.createElement("tr");
                        var td1 = document.createElement("td");
                        td1.style.textAlign = "center";
                        td1.innerHTML = cnt++;
                        var td2 = document.createElement("td");
                        td2.innerHTML = '[ ' + s.attributes.group_service + '] ' + s.attributes.label;
                        tr.append(td1);
                        tr.append(td2);
                        a.append(tr);
                    });

                    var e = document.createElement("div");
                    let html = `<div style="position: absolute; right: 0; width: 200px;">
                    <div style="display: flex;">
                            <prev style="text-align: right; right: 10px;">Ngày [DAY] Tháng
                                    [MONTH] Năm [YEAR] </prev>
                    </div>
                    <div style="display: flex;">
                            <prev style="text-align: center; width: 100%; top: 30px;">Bác
                                    sĩ điều trị </prev>
                    </div>
                    [SIGNATURE_IMAGE_URL]
                    [DOCTOR_NAME]
            </div>`.replaceAll("[DAY]", date).replaceAll("[MONTH]", month + 1).replaceAll("[YEAR]", year);

                    if (doctorInCharge?.signature?.url) {
                        html = html.replaceAll("[SIGNATURE_IMAGE_URL]", `<img style="right: 0; width: 170px;" src="https://api.echomedi.com${doctorInCharge?.signature?.url ?? ''}"/>`);
                    } else {
                        html = html.replaceAll("[SIGNATURE_IMAGE_URL]", '<div style="height: 80px; width: 170px;"></div>');
                    }

                    if (doctorInCharge?.patient?.full_name) {
                        html = html.replace("[DOCTOR_NAME]", `<div style="display: flex;"><prev style="text-align: center; width: 100%; ">${doctorInCharge?.patient?.full_name}</prev></div>`);
                    } else {
                        html = html.replace("[DOCTOR_NAME]", '');
                    }

                    e.innerHTML = html;
                    tableContainer.append(e);

                    if (key == "1" && Array.isArray(groupByCategory["2"]) && groupByCategory["2"].length > 0) {
                        var b = a.cloneNode();
                        // var c = document.createElement("div");
                        // c.innerHTML = `<h1 style="text-align: center;font-size: 25px; margin-bottom: 50px; page-break-before: always;">PHIẾU CHỈ ĐỊNH CẬN LÂM SÀNG</h1>`;
                        // tableContainer.append(c);

                        var d = document.createElement("div");
                        d.innerHTML = h;
                        d.classList.add("page-break-after-el");
                        tableContainer.append(d);


                        tableContainer.append(b);
                        a = b;
                        cnt = 1;

                        var tr = document.createElement("tr");
                        var td1 = document.createElement("td");
                        td1.style.textAlign = "center";
                        td1.innerHTML = "Stt";
                        var td2 = document.createElement("td");
                        td2.innerHTML = "Tên xét nghiệm";
                        tr.append(td1);
                        tr.append(td2);
                        a.append(tr);
                    }
                });
            }, groupByCategory, h, dayjs().date(), dayjs().month(), dayjs().year(), doctorInCharge);

            var a = await page.createPDFStream(
                {
                    printBackground: true,
                    format: 'A4',
                    margin: {
                        top: "120px",
                        left: "70px",
                        right: "70px",
                        bottom: "130px",
                    },
                    displayHeaderFooter: true,
                    headerTemplate: body("PHIẾU CHỈ ĐỊNH CẬN LÂM SÀNG", 18),
                    footerTemplate: footer,
                }
            );

            ctx.send(a);
            a.on('close', async () => {
                try {
                    await page.close();
                    await browser.close();
                } catch (e) {

                }
            });
        },
        async generatePrescription(ctx) {
            const browser = await puppeteer.launch({
                headless: true
            });

            let order = await strapi
                .query('api::medical-record.medical-record')
                .findOne({
                    where: { id: ctx.request.body.id },
                    populate: {
                        patient: true,
                        doctor_in_charge: { populate: true },
                        prescription: {
                            populate: {
                                Drugs: {
                                    populate: {
                                        drug: true,
                                    }
                                }
                            }
                        }
                    }
                });

            const doctorInCharge = order.doctor_in_charge;

            let prescription = await strapi
                .query('api::prescription.prescription')
                .findOne({
                    where: { id: ctx.request.body.pr_id },
                    populate: {
                        Drugs: {
                            populate: {
                                drug: true,
                            }
                        }
                    }
                });

            const patient = order.patient;

            const address = patient?.address
                ? `${patient?.address?.address || ""}, ${patient?.address?.ward?.name || ""}, ${patient?.address?.district?.name || ""
                }, ${patient?.address?.province?.name || ""}`
                : "-";


            const drugs = prescription.Drugs
            // create a new page
            const page = await browser.newPage();
            // set your html as the pages content
            let html = fs.readFileSync(`${__dirname}/drug.html`, 'utf8');

            html = html.replace("[DATE]", dayjs(order.createdAt).date());
            html = html.replace("[MONTH]", dayjs(order.createdAt).month() + 1);
            html = html.replace("[YEAR]", dayjs(order.createdAt).year());
            html = html.replace("[AGE]", `${dayjs(patient.birthday).year()} (${(dayjs().year() - dayjs(patient.birthday).year())})`);
            html = html.replace("[CREATED_DAY]", dayjs(order.createdAt).utc().format("DD/MM/YYYY"));
            html = html.replace("[GIOI_TINH]", patient.gender == "male" ? "Nam" : "Nữ");
            html = html.replace("[FULL_NAME]", patient.full_name?.toUpperCase());
            html = html.replace("[ADDRESS]", address);
            html = html.replace("[EMAIL]", patient.email);
            html = html.replace("[UID]", patient.uid);
            html = html.replace("[SDT]", patient.phone);
            html = html.replace("[LOI_DAN]", prescription?.message ?? "");
            html = html.replace("[RE_EXAM_DATE]", prescription?.reExaminationDate ? dayjs(prescription?.reExaminationDate).add(7, 'hour').utc().format("DD/MM/YYYY") : "");
            html = html.replace("[MAIN_DIAGNOSE]", getValueJsonInlineSeparator(order.main_diagnose));
            html = html.replace("[OTHER_DIAGNOSE]", getValueJsonInlineSeparator(order.other_diagnose));
            html = html.replace("[CHAN_DOAN]", getValueJsonInlineSeparator(order.chan_doan));

            if (doctorInCharge?.signature?.url) {
                html = html.replace("[SIGNATURE_IMAGE_URL]", `<img style="right: 0; width: 170px;" src="https://api.echomedi.com${doctorInCharge?.signature?.url ?? ''}"/>`);
            } else {
                html = html.replace("[SIGNATURE_IMAGE_URL]", '<div style="height: 80px; width: 170px;"></div>');
            }

            if (doctorInCharge?.patient?.full_name) {
                html = html.replace("[DOCTOR_NAME]", `<div style="display: flex;"><prev style="text-align: center; width: 100%; ">${doctorInCharge?.patient?.full_name}</prev></div>`);
            } else {
                html = html.replace("[DOCTOR_NAME]", '');
            }

            await page.setContent(html, {
                waitUntil: 'networkidle0'
            });

            page.on('console', async (msg) => {
                const msgArgs = msg.args();
                for (let i = 0; i < msgArgs.length; ++i) {
                }
            });

            await page.evaluate((drugs, bs) => {
                var a = document.getElementById('table');
                let i = 1;
                drugs.forEach(s => {
                    let html = `<td style="line-height: 25px;"><prev style="">[TEN_THUOC]     </prev>
                    <pre style="">[SANG][TRUA][CHIEU][TOI]</pre>
                    <prev style="margin-top: 10px;">Cách dùng: [CACH_DUNG]</prev></td><td><span>${s.amount} ${s.unit}</span></td>
                    `;
                    html = html.replace("[TEN_THUOC]", `<span>` + i + "</span>" + ". " + s.drug?.label + (s.drug.ingredient ? ` (${s.drug.ingredient})` : ""));
                    html = html.replace("[SO_LUONG]", s.amount);
                    html = html.replace("[SANG]", s.morningAmount && s.morningAmount ? `Sáng  ${s.morningAmount}  ${s.unit}       ` : '');
                    html = html.replace("[TRUA]", s.noonAmount && s.noonAmount ? `Trưa  ${s.noonAmount}  ${s.unit}       ` : '');
                    html = html.replace("[CHIEU]", s.afternoonAmount && s.afternoonAmount ? `Chiều  ${s.afternoonAmount}  ${s.unit}       ` : '');
                    html = html.replace("[TOI]", s.eveningAmount && s.eveningAmount ? `Tối  ${s.eveningAmount}  ${s.unit}       ` : '');

                    html = html.replace("[TRUA]", s.noonAmount);
                    html = html.replace("[CHIEU]", s.afternoonAmount);
                    html = html.replace("[TOI]", s.eveningAmount);
                    html = html.replace("[CACH_DUNG]", s.usage);
                    var li = document.createElement("tr");
                    li.innerHTML = html;
                    li.style.marginBottom = "5px";
                    ++i;
                    a.append(li);
                });
            }, drugs);

            // var a = await page.createPDFStream({ printBackground: true, width: "1118px", height: "1685px" });
            var a = await page.createPDFStream(
                {
                    printBackground: true,
                    format: 'A4',
                    margin: {
                        top: "120px",
                        left: "60px",
                        right: "60px",
                        bottom: "130px",
                    },
                    displayHeaderFooter: true,
                    headerTemplate: body("ĐƠN THUỐC", 25),
                    footerTemplate: footer,
                }
            );
            ctx.send(a);
            a.on('close', async () => {
                try {
                    await page.close();
                    await browser.close();
                } catch (e) {

                }
            });
        },
        async generateAdditionalPrescription(ctx) {
            const browser = await puppeteer.launch({
                headless: true
            });

            let order = await strapi
                .query('api::medical-record.medical-record')
                .findOne({
                    where: { id: ctx.request.body.id },
                    populate: {
                        patient: true,
                        doctor_in_charge: { populate: true },
                        prescription: {
                            populate: {
                                additional_drugs: {
                                    populate: {
                                        drug: true,
                                    }
                                }
                            }
                        }
                    }
                });

            const patient = order.patient;

            const address = patient?.address
                ? `${patient?.address?.address || ""}, ${patient?.address?.ward?.name || ""}, ${patient?.address?.district?.name || ""
                }, ${patient?.address?.province?.name || ""}`
                : "-";


            const drugs = order.prescription.additional_drugs;
            // create a new page
            const page = await browser.newPage();
            // set your html as the pages content
            let html = fs.readFileSync(`${__dirname}/additional_drug.html`, 'utf8');

            html = html.replace("[DATE]", dayjs(order.createdAt).date());
            html = html.replace("[MONTH]", dayjs(order.createdAt).month() + 1);
            html = html.replace("[YEAR]", dayjs(order.createdAt).year());
            html = html.replace("[AGE]", `${dayjs(patient.birthday).year()} (${(dayjs().year() - dayjs(patient.birthday).year())})`);
            html = html.replace("[CREATED_DAY]", dayjs(order.createdAt).utc().format("DD/MM/YYYY"));
            html = html.replace("[GIOI_TINH]", patient.gender == "male" ? "Nam" : "Nữ");
            html = html.replace("[FULL_NAME]", patient.full_name?.toUpperCase());
            html = html.replace("[ADDRESS]", address);
            html = html.replace("[EMAIL]", patient.email);
            html = html.replace("[UID]", patient.uid);
            html = html.replace("[SDT]", patient.phone);
            html = html.replace("[LOI_DAN]", order.prescription?.message ?? "");
            html = html.replace("[RE_EXAM_DATE]", order.prescription?.reExaminationDate ? dayjs(order.prescription?.reExaminationDate).utc().format("DD/MM/YYYY") : "");
            html = html.replace("[CHAN_DOAN]", getValueJsonInlineSeparator(order.chan_doan));

            const doctorInCharge = order.doctor_in_charge;
            if (doctorInCharge?.signature?.url) {
                html = html.replace("[SIGNATURE_IMAGE_URL]", `<img style="right: 0; width: 170px;" src="https://api.echomedi.com${doctorInCharge?.signature?.url ?? ''}"/>`);
            } else {
                html = html.replace("[SIGNATURE_IMAGE_URL]", '<div style="height: 80px; width: 170px;"></div>');
            }

            if (doctorInCharge?.patient?.full_name) {
                html = html.replace("[DOCTOR_NAME]", `<div style="display: flex;"><prev style="text-align: center; width: 100%; ">${doctorInCharge?.patient?.full_name}</prev></div>`);
            } else {
                html = html.replace("[DOCTOR_NAME]", '');
            }

            await page.setContent(html, {
                waitUntil: 'networkidle0'
            });

            page.on('console', async (msg) => {
                const msgArgs = msg.args();
                for (let i = 0; i < msgArgs.length; ++i) {
                }
            });

            await page.evaluate((drugs, bs) => {
                let tableContainer = document.getElementById('table-container');
                var a = document.getElementById('table');
                let i = 1;
                drugs.forEach(s => {
                    let html = `<td style="line-height: 25px;"><prev style="">[TEN_THUOC]     </prev>
                    <pre style="">[SANG][TRUA][CHIEU][TOI]</pre>
                    <prev style="margin-top: 10px;">Cách dùng: [CACH_DUNG]</prev></td><td><span>${s.amount} ${s.unit}</span></td>
                    `;
                    html = html.replace("[TEN_THUOC]", `<span>` + i + "</span>" + ". " + s.drug?.label + (s.drug.ingredient ? ` (${s.drug.ingredient})` : ""));
                    html = html.replace("[SO_LUONG]", s.amount);
                    html = html.replace("[SANG]", s.morningAmount && s.morningAmount ? `Sáng  ${s.morningAmount}  ${s.unit}       ` : '');
                    html = html.replace("[TRUA]", s.noonAmount && s.noonAmount ? `Trưa  ${s.noonAmount}  ${s.unit}       ` : '');
                    html = html.replace("[CHIEU]", s.afternoonAmount && s.afternoonAmount ? `Chiều  ${s.afternoonAmount}  ${s.unit}       ` : '');
                    html = html.replace("[TOI]", s.eveningAmount && s.eveningAmount ? `Tối  ${s.eveningAmount}  ${s.unit}       ` : '');

                    html = html.replace("[TRUA]", s.noonAmount);
                    html = html.replace("[CHIEU]", s.afternoonAmount);
                    html = html.replace("[TOI]", s.eveningAmount);
                    html = html.replace("[CACH_DUNG]", s.usage);
                    var li = document.createElement("tr");
                    li.innerHTML = html;
                    li.style.marginBottom = "10px";
                    a.append(li);
                    ++i;
                });
            }, drugs);

            var a = await page.createPDFStream(
                {
                    printBackground: true,
                    format: 'A4',
                    margin: {
                        top: "120px",
                        left: "60px",
                        right: "60px",
                        bottom: "130px",
                    },
                    displayHeaderFooter: true,
                    headerTemplate: body("PHIẾU TƯ VẤN SẢN PHẦM HỖ TRỢ ĐIỀU TRỊ", 12),
                    footerTemplate: footer,
                }
            );
            ctx.send(a);
            a.on('close', async () => {
                try {
                    await page.close();
                    await browser.close();
                } catch (e) {

                }
            });
        }
    }));


const getValueJson = (value) => {
    let js;
    try {
        js = JSON.parse(value);
        return js.map(j => `<div style="margin-top: 5px;><prev style="margin-top: -5px;">${j.value}</prev></div>`).join("");
    } catch (e) {
        return `<div style="white-space: pre-line; margin-top: 5px;">${value ?? ""}</div>`;
    } finally {
    }
}

const getValueJsonInline = (value) => {
    let js;
    try {
        js = JSON.parse(value);
        return js.map(j => `<prev style="margin-top: -5px;">${j.value}</prev>`);
    } catch (e) {
        return `<div style="white-space: pre-line; margin-top: 5px;">${value ?? ""}</div>`;
    } finally {
    }
    // return js.map(j => `<prev style="margin-top: -5px;">${j.value}</prev>`).join("");
}

const getValueJsonInlineSeparator = (value) => {
    let js;
    try {
        js = JSON.parse(value);
    } catch (e) {
        js = [];
    } finally {
        js = [{ value }];
    }
    return js.map(j => `<prev style="margin-top: -5px;">${j.value ?? ''}</prev>`).join(", ");
}

const parseAddress = (address) => {
    const add = address
        ? `${address?.address ? (address?.address + ",") : ''} ${address?.ward?.name ? (address?.ward?.name + ",") : ""} ${address?.district?.name ? (address?.district?.name + ',') : ""
        } ${address?.province?.name ? (address?.province?.name) : ""}`
        : "-";
    return add;
}

function monthDiff(d1, d2) {
    const dayDiff = d2.diff(d1, 'day') // 7
    const monthDiff = d2.diff(d1, 'month') // 7
    const yearDiff = d2.diff(d1, 'year') // 7
    
    if (dayDiff <= 28) return dayDiff + ' ngày';
    if (yearDiff >= 5) return yearDiff + ' năm';
    return monthDiff + ' tháng';
}