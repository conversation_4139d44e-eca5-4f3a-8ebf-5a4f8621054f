'use strict';

/**
 * service-bundle controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::service-bundle.service-bundle',
    ({ strapi }) => ({
        async getGoldBundleServices(ctx) {
            const { patientId, membership } = ctx.params;

            let ms = await strapi.query("api::service-bundle.service-bundle").findMany({
                populate: {
                    membership_discount: true,
                    medical_services: true,
                    Locations: true,
                }
            });

            const patient = await strapi
                .query("api::patient.patient")
                .findOne({ where: { id: patientId }, populate: true });

            let results = [];
            await Promise.all(ms.map(async s => {
                if (s.membership_discount == null) {
                    results.push({
                        "id": s.id,
                        "attributes": s
                    });
                } else {
                    if (s.membership_discount.platinum_yearly != null && s.membership_discount.platinum_yearly != 0 && (membership == "platinum" || patient.membership == "platinum")) {
                        var firstDay = new Date();
                        firstDay.setDate(firstDay.getDate() - 365);
                        var lastDay = new Date();
                        let msu = await strapi.query("api::bundle-service-usage.bundle-service-usage").findMany({
                            where: {
                                patient: patientId,
                                service_bundle: s.id,
                                publishedAt: {
                                    $gte: firstDay,
                                    $lte: lastDay,
                                },
                            },
                            populate: {
                                membership_discount: true
                            }
                        });
    
                        if (msu.length < s.membership_discount.platinum_yearly) {
                            s["original_price"] = s["price"];
                            s["discount_note"] = "Thành viên bạch kim";
                            s["discount_percentage"] = 100;
                            s["membership_gold"] = true;
                            s["price"] = 0;
                            
                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        } else {
                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        }
                    } else if (s.membership_discount.gold_yearly != null && s.membership_discount.gold_yearly != 0 && (membership == "gold" || patient.membership == "gold")) {
                        var firstDay = new Date();
                        firstDay.setDate(firstDay.getDate() - 365);
                        var lastDay = new Date();
                        let msu = await strapi.query("api::bundle-service-usage.bundle-service-usage").findMany({
                            where: {
                                patient: patientId,
                                service_bundle: s.id,
                                publishedAt: {
                                    $gte: firstDay,
                                    $lte: lastDay,
                                },
                            },
                            populate: {
                                membership_discount: true
                            }
                        });  
    
                        if (msu.length < s.membership_discount.gold_yearly) {
                            s["original_price"] = s["price"];
                            s["discount_note"] = "Thành viên vàng";
                            s["discount_percentage"] = 100;
                            s["membership_gold"] = true;
                            s["price"] = 0;
                            
                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        } else {
                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        }
                    } else if (s.membership_discount.medical_provider_yearly != null && s.membership_discount.medical_provider_yearly != 0 && (membership == "medical_provider" || patient.membership == "medical_provider")) {
                        var firstDay = new Date();
                        firstDay.setDate(firstDay.getDate() - 365);
                        var lastDay = new Date();
                        let msu = await strapi.query("api::bundle-service-usage.bundle-service-usage").findMany({
                            where: {
                                patient: patientId,
                                service_bundle: s.id,
                                publishedAt: {
                                    $gte: firstDay,
                                    $lte: lastDay,
                                },
                            },
                            populate: {
                                membership_discount: true
                            }
                        });  
    
                        if (msu.length < s.membership_discount.medical_provider_yearly) {
                            s["original_price"] = s["price"];
                            s["discount_note"] = "Medical provider";
                            s["discount_percentage"] = 100;
                            s["membership_gold"] = true;
                            s["price"] = 0;
                            
                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        } else {
                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        }
                    } else if (s.membership_discount.gold_monthly != null && s.membership_discount.gold_monthly != 0 && (membership == "gold" || patient.membership == "gold")) {
                        var date = new Date();
                        var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                        var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                        let msu = await strapi.query("api::bundle-service-usage.bundle-service-usage").findMany({
                            where: {
                                patient: patientId,
                                service_bundle: s.id,
                                publishedAt: {
                                    $gte: firstDay,
                                    $lte: lastDay,
                                },
                            }
                        });

                        if (msu.length < s.membership_discount.gold_monthly) {
                            s["original_price"] = s["price"];
                            s["discount_note"] = "Thành viên vàng";
                            s["discount_percentage"] = 100;
                            s["membership_gold"] = true;
                            s["price"] = 0;

                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        } else {
                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        }
                    } else if (s.membership_discount.platinum_monthly != null && s.membership_discount.platinum_monthly != 0 && (membership == "platinum" || patient.membership == "platinum")) {
                        var date = new Date();
                        var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                        var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                        let msu = await strapi.query("api::bundle-service-usage.bundle-service-usage").findMany({
                            where: {
                                patient: patientId,
                                service_bundle: s.id,
                                publishedAt: {
                                    $gte: firstDay,
                                    $lte: lastDay,
                                },
                            },
                            populate: {
                                membership_discount: true
                            }
                        });

                        if (msu.length < s.membership_discount.platinum_monthly) {
                            s["original_price"] = s["price"];
                            s["discount_note"] = "Thành viên bạch kim";
                            s["discount_percentage"] = 100;
                            s["membership_gold"] = true;
                            s["price"] = 0;

                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        } else {
                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        }
                    } else if (s.membership_discount.medical_provider_monthly != null && s.membership_discount.medical_provider_monthly != 0 && (membership == "medical_provider" || patient.membership == "medical_provider")) {
                        var date = new Date();
                        var firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
                        var lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
                        let msu = await strapi.query("api::bundle-service-usage.bundle-service-usage").findMany({
                            where: {
                                patient: patientId,
                                service_bundle: s.id,
                                publishedAt: {
                                    $gte: firstDay,
                                    $lte: lastDay,
                                },
                            },
                            populate: {
                                membership_discount: true
                            }
                        });

                        if (msu.length < s.membership_discount.medical_provider_monthly) {
                            s["original_price"] = s["price"];
                            s["discount_note"] = "Medical provider";
                            s["discount_percentage"] = 100;
                            s["membership_gold"] = true;
                            s["price"] = 0;

                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        } else {
                            results.push({
                                "id": s.id,
                                "attributes": s
                            });
                        }
                    } else if (membership == "family" || patient.membership == "family") {
                        s["original_price"] = s["price"];
                        s["discount_note"] = "Family doctor package";
                        s["discount_percentage"] = 10;
                        s["membership_gold"] = true;
                        s["price"] = s["price"] * 0.9;
    
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }  
                    
                    else {
                        results.push({
                            "id": s.id,
                            "attributes": s
                        });
                    }
                }
            }));

            return { data: results };
        },

        async setLabelI(ctx) {
            let sb = await strapi.query("api::service-bundle.service-bundle").findMany({});

            sb.forEach(p => {
                strapi
                    .query('api::service-bundle.service-bundle')
                    .update({
                        where: { id: p.id },
                        data: {
                            label_i: removeVietnameseTones(p.label ?? "")
                        }
                    });
            })

            return [];
        }
    }
    ));
function removeVietnameseTones(str) {
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a")
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e")
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i")
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o")
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u")
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y")
    str = str.replace(/đ/g, "d")
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A")
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E")
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I")
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O")
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U")
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y")
    str = str.replace(/Đ/g, "D")
    // Some system encode vietnamese combining accent as individual utf-8 characters
    // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, "") // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, "") // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g, " ")
    str = str.trim()
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    str = str.replace(
        /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,
        " "
    )
    return str
}
