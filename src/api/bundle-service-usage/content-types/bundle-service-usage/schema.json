{"kind": "collectionType", "collectionName": "bundle_service_usages", "info": {"singularName": "bundle-service-usage", "pluralName": "bundle-service-usages", "displayName": "Bundle Service Usage"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"service_bundle": {"type": "relation", "relation": "oneToOne", "target": "api::service-bundle.service-bundle"}, "patient": {"type": "relation", "relation": "oneToOne", "target": "api::patient.patient"}}}