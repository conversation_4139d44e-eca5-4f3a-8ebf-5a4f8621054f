{"kind": "collectionType", "collectionName": "tasks", "info": {"singularName": "task", "pluralName": "tasks", "displayName": "Task", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "board": {"type": "relation", "relation": "manyToOne", "target": "api::board.board", "inversedBy": "tasks"}, "list": {"type": "relation", "relation": "manyToOne", "target": "api::list.list", "inversedBy": "tasks"}, "uid": {"type": "string"}, "description": {"type": "richtext"}, "attachments": {"type": "json"}, "labels": {"type": "json"}, "comments": {"type": "json"}, "assigments": {"type": "json"}, "coverImage": {"type": "string"}, "members": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "inversedBy": "tasks"}}}