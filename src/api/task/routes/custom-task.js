module.exports = {
    routes: [
      {
        method: "POST",
        path: "/tasks/task-update-property",
        handler: "task.updateProperty",
        config: {
          policies: [],
          prefix: "",
        },
      },
      {
        method: "POST",
        path: "/tasks/email-notify-assigned-user",
        handler: "task.emailNotifyAssignedUser",
        config: {
          policies: [],
          prefix: "",
        },
      },
      {
        method: "POST",
        path: "/tasks/approve-task",
        handler: "task.approveTask",
        config: {
          policies: [],
          prefix: "",
        },
      },
      {
        method: "GET",
        path: "/tasks/get-tasks",
        handler: "task.getTasks",
        config: {
          policies: [],
          prefix: "",
        },
      }
    ]
}