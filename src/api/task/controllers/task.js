'use strict';

/**
 * task controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const nodemailer = require('nodemailer');
const AWS = require('aws-sdk');

module.exports = createCoreController('api::task.task', ({ strapi }) => ({



  async updateProperty(ctx) {
    const {
      taskId,
      property,
      data
    } = ctx.request.body;

    // var task = await strapi.query("api::task.task")
    //   .update({
    //     where: { uid: taskId },
    //     data: { [property]: data }
    //   });

    var task;
    if (property == 'assigments') {
      let members = [];
      data?.forEach(d => {
        members.push(d.id);
      })

      task = await strapi.query("api::task.task")
        .update({
          where: { uid: taskId },
          data: { [property]: data, members }
        });
    } else {
      task = await strapi.query("api::task.task")
        .update({
          where: { uid: taskId },
          data: { [property]: data }
        });
    }

    return task;
  },

  async approveTask(ctx) {
    const {
      taskId,
      userId,
      approve,
    } = ctx.request.body;

    var task = await strapi.query("api::task.task")
      .findOne({
        where: { id: taskId },
        populate: {
          list: true, board: {
            populate: {
              lists: true
            }
          }
        }
      });

    let assigments = task.assigments.map(a => {
      if (a.id == userId) a.approved = approve;
      return a;
    });

    const listOrder = task.board.lists.map(l => l.uid);
    const index = listOrder.indexOf(task.list.uid);


    let home = await strapi.query("api::list.list")
      .findOne({
        where: { uid: listOrder[index] },
        populate: { tasks: true }
      });
    let homeIDs = home.tasks.map(t => t.id);
    homeIDs.splice(taskId, 1);

    let foreign = await strapi.query("api::list.list")
      .findOne({
        where: { uid: listOrder[index + 1] },
        populate: { tasks: true }
      });
    console.log('asd', index, listOrder);
    if (!Array.isArray(foreign?.tasks)) foreign.tasks = [];
    let foreignIDs = foreign.tasks.map(t => t.id);
    foreignIDs.splice(0, 0, taskId);

    const approvalType = task.list.approvalType;
    if (approvalType == "any") {
      const exist = task.assigments.some((s) => s.approved);
      if (exist) {
        await strapi.query("api::list.list")
          .update({
            where: { uid: listOrder[index] },
            data: { tasks: homeIDs }
          });

        await strapi.query("api::list.list")
          .update({
            where: { uid: listOrder[index + 1] },
            data: { tasks: foreignIDs }
          });
        await strapi.query("api::task.task")
          .update({
            where: { id: taskId },
            data: { assigments: foreign.defaultAssigments }
          });
      }

      return { approved: true }
    } else {
      const approved = task.assigments.every((s) => s.approved);
      if (approved) {
        await strapi.query("api::list.list")
          .update({
            where: { uid: listOrder[index] },
            data: { tasks: homeIDs }
          });

        await strapi.query("api::list.list")
          .update({
            where: { uid: listOrder[index + 1] },
            data: { tasks: foreignIDs }
          });
        await strapi.query("api::task.task")
          .update({
            where: { id: taskId },
            data: { assigments: foreign.defaultAssigments }
          });
      } else {
        await strapi.query("api::task.task")
          .update({
            where: { id: taskId },
            data: { assigments: task.assigments }
          });
      }

      return { approved }
    }

    return task;
  },

  async emailNotifyAssignedUser(ctx) {
    const user = await strapi
      .query("plugin::users-permissions.user")
      .findOne({ where: { id: ctx.request.body.userId } });
    const task = await strapi
      .query("api::task.task")
      .findOne({
        where: { uid: ctx.request.body.taskId }, populate: {
          board: true,
        }
      });
    const emailTemplate = await strapi
      .query("api::email-template.email-template")
      .findOne({ where: { id: 1 }, });

    // configure AWS SDK
    AWS.config.update({
      accessKeyId: process.env.AWS_SES_KEY,
      secretAccessKey: process.env.AWS_SES_SECRET,
      region: "us-east-1",
    });

    // create Nodemailer SES transporter
    let transporter = nodemailer.createTransport({
      SES: new AWS.SES({
        apiVersion: '2010-12-01'
      })
    });

    let html = emailTemplate.article.replaceAll("[TASK_URL]", `http://admin.echomedi.com/board/${task.board.id}/${task.id}`);

    // send some mail
    transporter.sendMail({
      from: '<EMAIL>',
      to: user.email,
      subject: emailTemplate.label,
      html,
    }, (err, info) => {
      console.log("sendEmail err", err);
      console.log("sendEmail", info);
    });

    // await strapi.plugins['email'].services.email.send({
    //   to: user.email,
    //   from: '<EMAIL>',
    //   subject: '[ECHOMEDI_BOARD] New task',
    //   html: emailTemplate.article,
    // });

    return {};
  },

  
  async getTasks(ctx) {
    const { id } = ctx.state.user;

    var tasks = await strapi.query("plugin::users-permissions.user").
        findMany({
          where: { id },
          populate: {
            tasks: {
              populate: {
                board: true,
              }
            }
          }
        });

    return tasks;
},
}));
