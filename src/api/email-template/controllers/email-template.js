'use strict';
const AWS = require('aws-sdk');
const nodemailer = require("nodemailer");

/**
 * email-template controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::email-template.email-template', ({ strapi }) => ({
    async sendEmails(ctx) {
        const sourceId = ctx.request.body.data.sourceId;
        let patients = [];
        let emailTemplate = await strapi.query("api::email-template.email-template").findOne({
            where: { id: ctx.request.body.data.id },
        });
        if (sourceId != -1) {
            patients = await strapi.query("api::patient.patient").findMany({
                where: { patient_source: ctx.request.body.data.sourceId }});
        } else {
            patients = await strapi.query("api::patient.patient").findMany({});
        }

        // configure AWS SDK
        AWS.config.update({
            accessKeyId: process.env.AWS_SES_KEY,
            secretAccessKey: process.env.AWS_SES_SECRET,
            region: "us-east-1",
        });

        // create Nodemailer SES transporter
        let transporter = nodemailer.createTransport({
            SES: new AWS.SES({
                apiVersion: '2010-12-01'
            })
        });

        patients.forEach(p => {
            let html = emailTemplate.article;
            if (p.email) {
                html = html.replaceAll('[FULL_NAME]', p.full_name);

                // send some mail
                transporter.sendMail({
                    from: '<EMAIL>',
                    to: p.email,
                    subject: emailTemplate.label,
                    html,
                }, (err, info) => {
                    console.log("sendEmail err", err);
                    console.log("sendEmail", info);
                });     
            }
        })

        return {ok: true};
    },

    async sendTestEmail(ctx) {
        const { id, email } = ctx.request.body.data;

        let emailTemplate = await strapi.query("api::email-template.email-template").findOne({
            where: { id },
        });

        // configure AWS SDK
        AWS.config.update({
            accessKeyId: process.env.AWS_SES_KEY,
            secretAccessKey: process.env.AWS_SES_SECRET,
            region: "us-east-1",
        });

        // create Nodemailer SES transporter
        let transporter = nodemailer.createTransport({
            SES: new AWS.SES({
                apiVersion: '2010-12-01'
            })
        });

        let html = emailTemplate.article;

        // send some mail
        transporter.sendMail({
            from: '<EMAIL>',
            to: email,
            subject: emailTemplate.label,
            html,
        }, (err, info) => {
            console.log("sendEmail err", err);
            console.log("sendEmail", info);
        });

        return emailTemplate;
    },
}));
