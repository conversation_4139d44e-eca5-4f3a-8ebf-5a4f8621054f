'use strict';

/**
 * cart controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::cart.cart', ({ strapi }) => ({
    // wrap a core action, leaving core logic in place
    async updateCartLine(ctx) {
      var { id, cnt } = ctx.request.body;
      return strapi.query("api::cart-line.cart-line").update({ where: {id}, data: { quantity: cnt} });
    }
}));
