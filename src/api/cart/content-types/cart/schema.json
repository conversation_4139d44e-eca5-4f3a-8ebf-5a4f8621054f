{"kind": "collectionType", "collectionName": "carts", "info": {"singularName": "cart", "pluralName": "carts", "displayName": "<PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"products": {"type": "relation", "relation": "manyToMany", "target": "api::product.product", "inversedBy": "carts"}, "users_permissions_user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "mappedBy": "cart"}, "cart_lines": {"type": "relation", "relation": "oneToMany", "target": "api::cart-line.cart-line", "mappedBy": "cart"}, "text": {"type": "string"}, "order": {"type": "relation", "relation": "oneToOne", "target": "api::order.order", "mappedBy": "cart"}}}