{"kind": "collectionType", "collectionName": "cart_lines", "info": {"singularName": "cart-line", "pluralName": "cart-lines", "displayName": "Cart Line", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"product": {"type": "relation", "relation": "oneToOne", "target": "api::product.product"}, "cart": {"type": "relation", "relation": "manyToOne", "target": "api::cart.cart", "inversedBy": "cart_lines"}, "service": {"type": "relation", "relation": "oneToOne", "target": "api::service.service"}, "quantity": {"type": "integer"}}}