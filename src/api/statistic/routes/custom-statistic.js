module.exports = {
  routes: [
    {
      method: "GET",
      path: "/statistics/dashboard",
      handler: "statistic.getDashboardStats",
      config: {
        policies: [],
        prefix: "",
      },
    },
    {
      method: "GET", 
      path: "/statistics/time-based",
      handler: "statistic.getTimeBasedStats",
      config: {
        policies: [],
        prefix: "",
      },
    },
    {
      method: "GET",
      path: "/statistics/top-stats", 
      handler: "statistic.getTopStats",
      config: {
        policies: [],
        prefix: "",
      },
    },
    {
      method: "GET",
      path: "/statistics/health-check",
      handler: "statistic.getSystemHealth",
      config: {
        policies: [],
        prefix: "",
      },
    },
    {
      method: "GET",
      path: "/statistics/services",
      handler: "statistic.getServiceStats",
      config: {
        policies: [],
        prefix: "",
      },
    },
    {
      method: "GET",
      path: "/statistics/custom-period",
      handler: "statistic.getCustomPeriodStats",
      config: {
        policies: [],
        prefix: "",
      },
    },
  ],
};
