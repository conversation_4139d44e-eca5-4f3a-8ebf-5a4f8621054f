{"kind": "collectionType", "collectionName": "statistics", "info": {"singularName": "statistic", "pluralName": "statistics", "displayName": "Statistic", "description": "System statistics and analytics"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "type": {"type": "enumeration", "enum": ["dashboard", "time-based", "top-stats", "health-check"], "default": "dashboard"}, "data": {"type": "json"}, "period": {"type": "string"}, "generated_at": {"type": "datetime"}}}