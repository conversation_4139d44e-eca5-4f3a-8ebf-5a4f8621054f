'use strict';

/**
 * statistic controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::statistic.statistic', ({ strapi }) => ({
    
    // API thống kê tổng quan
    async getDashboardStats(ctx) {
        try {
            const today = new Date();
            const startOfDay = new Date(today.setHours(0, 0, 0, 0));
            const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const startOfYear = new Date(today.getFullYear(), 0, 1);

            // Thống kê patients
            const totalPatients = await strapi.db.query('api::patient.patient').count();
            const newPatientsToday = await strapi.db.query('api::patient.patient').count({
                where: { createdAt: { $gte: startOfDay } }
            });
            const newPatientsThisWeek = await strapi.db.query('api::patient.patient').count({
                where: { createdAt: { $gte: startOfWeek } }
            });
            const newPatientsThisMonth = await strapi.db.query('api::patient.patient').count({
                where: { createdAt: { $gte: startOfMonth } }
            });

            // Thống kê users
            const totalUsers = await strapi.db.query('plugin::users-permissions.user').count();
            const activeUsersToday = await strapi.db.query('plugin::users-permissions.user').count({
                where: { 
                    blocked: false,
                    confirmed: true,
                    updatedAt: { $gte: startOfDay }
                }
            });

            // Thống kê drugs
            const totalDrugs = await strapi.db.query('api::drug.drug').count();
            const expiredDrugs = await strapi.db.query('api::drug.drug').count({
                where: { endDate: { $lt: new Date() } }
            });
            const expiringSoonDrugs = await strapi.db.query('api::drug.drug').count({
                where: { 
                    endDate: { 
                        $gte: new Date(),
                        $lt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
                    }
                }
            });

            return ctx.send({
                status: 0,
                message: 'Dashboard statistics retrieved successfully',
                data: {
                    patients: {
                        total: totalPatients,
                        newToday: newPatientsToday,
                        newThisWeek: newPatientsThisWeek,
                        newThisMonth: newPatientsThisMonth,
                        growthRate: totalPatients > 0 ? ((newPatientsThisMonth / totalPatients) * 100).toFixed(2) : 0
                    },
                    users: {
                        total: totalUsers,
                        activeToday: activeUsersToday,
                        activeRate: totalUsers > 0 ? ((activeUsersToday / totalUsers) * 100).toFixed(2) : 0
                    },
                    drugs: {
                        total: totalDrugs,
                        expired: expiredDrugs,
                        expiringSoon: expiringSoonDrugs,
                        healthyStock: totalDrugs - expiredDrugs - expiringSoonDrugs
                    },
                    summary: {
                        totalEntities: totalPatients + totalUsers + totalDrugs,
                        systemHealth: expiredDrugs === 0 ? 'Excellent' : expiredDrugs < 5 ? 'Good' : 'Needs Attention',
                        lastUpdated: new Date().toISOString()
                    }
                }
            });

        } catch (error) {
            console.error('Error getting dashboard stats:', error);
            return ctx.internalServerError('Failed to retrieve dashboard statistics');
        }
    },

    // API thống kê theo thời gian
    async getTimeBasedStats(ctx) {
        const { period = 'month', year = new Date().getFullYear() } = ctx.query;

        try {
            let groupBy, dateFormat;
            
            switch (period) {
                case 'day':
                    groupBy = 'day';
                    dateFormat = '%Y-%m-%d';
                    break;
                case 'week':
                    groupBy = 'week';
                    dateFormat = '%Y-%u';
                    break;
                case 'month':
                    groupBy = 'month';
                    dateFormat = '%Y-%m';
                    break;
                case 'year':
                    groupBy = 'year';
                    dateFormat = '%Y';
                    break;
                default:
                    groupBy = 'month';
                    dateFormat = '%Y-%m';
            }

            // Thống kê patients theo thời gian
            const patientStats = await strapi.db.connection.raw(`
                SELECT 
                    DATE_FORMAT(created_at, '${dateFormat}') as period,
                    COUNT(*) as count
                FROM patients 
                WHERE YEAR(created_at) = ?
                GROUP BY DATE_FORMAT(created_at, '${dateFormat}')
                ORDER BY period
            `, [year]);

            // Thống kê users theo thời gian
            const userStats = await strapi.db.connection.raw(`
                SELECT 
                    DATE_FORMAT(created_at, '${dateFormat}') as period,
                    COUNT(*) as count
                FROM up_users 
                WHERE YEAR(created_at) = ?
                GROUP BY DATE_FORMAT(created_at, '${dateFormat}')
                ORDER BY period
            `, [year]);

            return ctx.send({
                status: 0,
                message: 'Time-based statistics retrieved successfully',
                data: {
                    period: period,
                    year: year,
                    patients: patientStats[0] || [],
                    users: userStats[0] || [],
                    generatedAt: new Date().toISOString()
                }
            });

        } catch (error) {
            console.error('Error getting time-based stats:', error);
            return ctx.internalServerError('Failed to retrieve time-based statistics');
        }
    },

    // API thống kê top entities
    async getTopStats(ctx) {
        const { limit = 10 } = ctx.query;

        try {
            // Top patients theo patient_source
            const topPatientSources = await strapi.db.connection.raw(`
                SELECT 
                    ps.name as source_name,
                    COUNT(p.id) as patient_count
                FROM patients p
                LEFT JOIN patient_sources ps ON p.patient_source = ps.id
                GROUP BY p.patient_source, ps.name
                ORDER BY patient_count DESC
                LIMIT ?
            `, [limit]);

            // Top drugs theo stock
            const topDrugsByStock = await strapi.db.query('api::drug.drug').findMany({
                select: ['code', 'label', 'stock', 'unit'],
                orderBy: { stock: 'desc' },
                limit: parseInt(limit)
            });

            // Top drugs sắp hết hạn
            const expiringSoonDrugs = await strapi.db.query('api::drug.drug').findMany({
                select: ['code', 'label', 'endDate', 'stock'],
                where: { 
                    endDate: { 
                        $gte: new Date(),
                        $lt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                    }
                },
                orderBy: { endDate: 'asc' },
                limit: parseInt(limit)
            });

            return ctx.send({
                status: 0,
                message: 'Top statistics retrieved successfully',
                data: {
                    topPatientSources: topPatientSources[0] || [],
                    topDrugsByStock: topDrugsByStock,
                    expiringSoonDrugs: expiringSoonDrugs,
                    generatedAt: new Date().toISOString()
                }
            });

        } catch (error) {
            console.error('Error getting top stats:', error);
            return ctx.internalServerError('Failed to retrieve top statistics');
        }
    },

    // API thống kê health check
    async getSystemHealth(ctx) {
        try {
            const now = new Date();
            
            // Check database connectivity
            const dbCheck = await strapi.db.query('api::patient.patient').count();
            
            // Check expired drugs
            const expiredDrugs = await strapi.db.query('api::drug.drug').count({
                where: { endDate: { $lt: now } }
            });

            // Check users without patients
            const usersWithoutPatients = await strapi.db.connection.raw(`
                SELECT COUNT(*) as count
                FROM up_users u
                LEFT JOIN patients p ON u.id = p.user
                WHERE p.id IS NULL AND u.blocked = 0
            `);

            // System health score calculation
            let healthScore = 100;
            const issues = [];

            if (expiredDrugs > 0) {
                healthScore -= Math.min(expiredDrugs * 5, 30);
                issues.push(`${expiredDrugs} expired drugs found`);
            }

            const orphanUsers = usersWithoutPatients[0][0].count;
            if (orphanUsers > 0) {
                healthScore -= Math.min(orphanUsers * 2, 20);
                issues.push(`${orphanUsers} users without patient records`);
            }

            let healthStatus;
            if (healthScore >= 90) healthStatus = 'Excellent';
            else if (healthScore >= 70) healthStatus = 'Good';
            else if (healthScore >= 50) healthStatus = 'Fair';
            else healthStatus = 'Poor';

            return ctx.send({
                status: 0,
                message: 'System health check completed',
                data: {
                    healthScore: Math.max(healthScore, 0),
                    healthStatus: healthStatus,
                    issues: issues,
                    checks: {
                        databaseConnectivity: dbCheck !== undefined,
                        expiredDrugs: expiredDrugs,
                        orphanUsers: orphanUsers
                    },
                    recommendations: issues.length > 0 ? [
                        'Review and update expired drugs',
                        'Link users to patient records',
                        'Regular system maintenance recommended'
                    ] : ['System is running optimally'],
                    lastChecked: now.toISOString()
                }
            });

        } catch (error) {
            console.error('Error in system health check:', error);
            return ctx.internalServerError('Failed to perform system health check');
        }
    }

}));
