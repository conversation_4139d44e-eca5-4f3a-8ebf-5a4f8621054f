'use strict';

/**
 * statistic controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::statistic.statistic', ({ strapi }) => ({
    
    // API thống kê tổng quan
    async getDashboardStats(ctx) {
        try {
            const today = new Date();
            const startOfDay = new Date(today.setHours(0, 0, 0, 0));
            const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const startOfYear = new Date(today.getFullYear(), 0, 1);

            // Thống kê patients
            const totalPatients = await strapi.db.query('api::patient.patient').count();
            const newPatientsToday = await strapi.db.query('api::patient.patient').count({
                where: { createdAt: { $gte: startOfDay } }
            });
            const newPatientsThisWeek = await strapi.db.query('api::patient.patient').count({
                where: { createdAt: { $gte: startOfWeek } }
            });
            const newPatientsThisMonth = await strapi.db.query('api::patient.patient').count({
                where: { createdAt: { $gte: startOfMonth } }
            });

            // Thống kê users
            const totalUsers = await strapi.db.query('plugin::users-permissions.user').count();
            const activeUsersToday = await strapi.db.query('plugin::users-permissions.user').count({
                where: { 
                    blocked: false,
                    confirmed: true,
                    updatedAt: { $gte: startOfDay }
                }
            });

            // Thống kê drugs
            const totalDrugs = await strapi.db.query('api::drug.drug').count();
            const expiredDrugs = await strapi.db.query('api::drug.drug').count({
                where: { endDate: { $lt: new Date() } }
            });
            const expiringSoonDrugs = await strapi.db.query('api::drug.drug').count({
                where: {
                    endDate: {
                        $gte: new Date(),
                        $lt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
                    }
                }
            });

            // Thống kê bookings
            const totalBookings = await strapi.db.query('api::booking.booking').count();
            const bookingsToday = await strapi.db.query('api::booking.booking').count({
                where: { createdAt: { $gte: startOfDay } }
            });
            const confirmedBookings = await strapi.db.query('api::booking.booking').count({
                where: { status: 'confirmed' }
            });
            const cancelledBookings = await strapi.db.query('api::booking.booking').count({
                where: { status: 'cancelled' }
            });

            // Thống kê orders
            const totalOrders = await strapi.db.query('api::order.order').count();
            const ordersToday = await strapi.db.query('api::order.order').count({
                where: { createdAt: { $gte: startOfDay } }
            });
            const completedOrders = await strapi.db.query('api::order.order').count({
                where: { status: 'done' }
            });

            // Thống kê revenue từ orders
            const revenueStats = await strapi.db.connection.raw(`
                SELECT
                    SUM(total) as total_revenue,
                    AVG(total) as avg_order_value,
                    SUM(CASE WHEN DATE(created_at) = CURDATE() THEN total ELSE 0 END) as today_revenue,
                    SUM(CASE WHEN WEEK(created_at) = WEEK(NOW()) AND YEAR(created_at) = YEAR(NOW()) THEN total ELSE 0 END) as week_revenue,
                    SUM(CASE WHEN MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW()) THEN total ELSE 0 END) as month_revenue
                FROM orders
                WHERE status = 'done'
            `);

            // Thống kê conversations
            const totalConversations = await strapi.db.query('api::conversation.conversation').count();
            const activeConversations = await strapi.db.query('api::conversation-queue.conversation-queue').count({
                where: { waiting: true }
            });

            // Thống kê packages
            const totalPackages = await strapi.db.query('api::package.package').count();

            // Thống kê articles/blogs
            const totalArticles = await strapi.db.query('api::article.article').count();
            const publishedArticles = await strapi.db.query('api::article.article').count({
                where: { publishedAt: { $ne: null } }
            });

            const revenue = revenueStats[0][0] || {};

            return ctx.send({
                status: 0,
                message: 'Dashboard statistics retrieved successfully',
                data: {
                    patients: {
                        total: totalPatients,
                        newToday: newPatientsToday,
                        newThisWeek: newPatientsThisWeek,
                        newThisMonth: newPatientsThisMonth,
                        growthRate: totalPatients > 0 ? ((newPatientsThisMonth / totalPatients) * 100).toFixed(2) : 0
                    },
                    users: {
                        total: totalUsers,
                        activeToday: activeUsersToday,
                        activeRate: totalUsers > 0 ? ((activeUsersToday / totalUsers) * 100).toFixed(2) : 0
                    },
                    bookings: {
                        total: totalBookings,
                        today: bookingsToday,
                        confirmed: confirmedBookings,
                        cancelled: cancelledBookings,
                        successRate: totalBookings > 0 ? ((confirmedBookings / totalBookings) * 100).toFixed(2) : 0
                    },
                    orders: {
                        total: totalOrders,
                        today: ordersToday,
                        completed: completedOrders,
                        completionRate: totalOrders > 0 ? ((completedOrders / totalOrders) * 100).toFixed(2) : 0
                    },
                    revenue: {
                        total: revenue.total_revenue || 0,
                        today: revenue.today_revenue || 0,
                        thisWeek: revenue.week_revenue || 0,
                        thisMonth: revenue.month_revenue || 0,
                        avgOrderValue: revenue.avg_order_value || 0
                    },
                    drugs: {
                        total: totalDrugs,
                        expired: expiredDrugs,
                        expiringSoon: expiringSoonDrugs,
                        healthyStock: totalDrugs - expiredDrugs - expiringSoonDrugs
                    },
                    communications: {
                        totalConversations: totalConversations,
                        activeConversations: activeConversations,
                        responseRate: totalConversations > 0 ? ((activeConversations / totalConversations) * 100).toFixed(2) : 0
                    },
                    content: {
                        totalPackages: totalPackages,
                        totalArticles: totalArticles,
                        publishedArticles: publishedArticles,
                        publishRate: totalArticles > 0 ? ((publishedArticles / totalArticles) * 100).toFixed(2) : 0
                    },
                    summary: {
                        totalEntities: totalPatients + totalUsers + totalDrugs + totalBookings + totalOrders,
                        systemHealth: expiredDrugs === 0 ? 'Excellent' : expiredDrugs < 5 ? 'Good' : 'Needs Attention',
                        lastUpdated: new Date().toISOString()
                    }
                }
            });

        } catch (error) {
            console.error('Error getting dashboard stats:', error);
            return ctx.internalServerError('Failed to retrieve dashboard statistics');
        }
    },

    // API thống kê theo thời gian (tháng/quý/năm)
    async getTimeBasedStats(ctx) {
        const { period = 'month', year = new Date().getFullYear(), quarter } = ctx.query;

        try {
            let groupBy, dateFormat, whereClause = '';

            switch (period) {
                case 'day':
                    groupBy = 'day';
                    dateFormat = '%Y-%m-%d';
                    whereClause = `WHERE YEAR(created_at) = ${year}`;
                    break;
                case 'week':
                    groupBy = 'week';
                    dateFormat = '%Y-%u';
                    whereClause = `WHERE YEAR(created_at) = ${year}`;
                    break;
                case 'month':
                    groupBy = 'month';
                    dateFormat = '%Y-%m';
                    whereClause = `WHERE YEAR(created_at) = ${year}`;
                    break;
                case 'quarter':
                    groupBy = 'quarter';
                    dateFormat = '%Y-Q%q';
                    whereClause = quarter ?
                        `WHERE YEAR(created_at) = ${year} AND QUARTER(created_at) = ${quarter}` :
                        `WHERE YEAR(created_at) = ${year}`;
                    break;
                case 'year':
                    groupBy = 'year';
                    dateFormat = '%Y';
                    whereClause = '';
                    break;
                default:
                    groupBy = 'month';
                    dateFormat = '%Y-%m';
                    whereClause = `WHERE YEAR(created_at) = ${year}`;
            }

            // Thống kê patients
            const patientStats = await strapi.db.connection.raw(`
                SELECT
                    DATE_FORMAT(created_at, '${dateFormat}') as period,
                    COUNT(*) as count
                FROM patients
                ${whereClause}
                GROUP BY DATE_FORMAT(created_at, '${dateFormat}')
                ORDER BY period
            `);

            // Thống kê users
            const userStats = await strapi.db.connection.raw(`
                SELECT
                    DATE_FORMAT(created_at, '${dateFormat}') as period,
                    COUNT(*) as count
                FROM up_users
                ${whereClause}
                GROUP BY DATE_FORMAT(created_at, '${dateFormat}')
                ORDER BY period
            `);

            // Thống kê bookings
            const bookingStats = await strapi.db.connection.raw(`
                SELECT
                    DATE_FORMAT(created_at, '${dateFormat}') as period,
                    COUNT(*) as count,
                    SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                    SUM(CASE WHEN status = 'finished' THEN 1 ELSE 0 END) as finished
                FROM bookings
                ${whereClause}
                GROUP BY DATE_FORMAT(created_at, '${dateFormat}')
                ORDER BY period
            `);

            // Thống kê orders
            const orderStats = await strapi.db.connection.raw(`
                SELECT
                    DATE_FORMAT(created_at, '${dateFormat}') as period,
                    COUNT(*) as count,
                    SUM(total) as revenue,
                    AVG(total) as avg_order_value,
                    SUM(CASE WHEN status = 'done' THEN 1 ELSE 0 END) as completed
                FROM orders
                ${whereClause}
                GROUP BY DATE_FORMAT(created_at, '${dateFormat}')
                ORDER BY period
            `);

            return ctx.send({
                status: 0,
                message: 'Time-based statistics retrieved successfully',
                data: {
                    period: period,
                    year: year,
                    quarter: quarter,
                    patients: patientStats[0] || [],
                    users: userStats[0] || [],
                    bookings: bookingStats[0] || [],
                    orders: orderStats[0] || [],
                    generatedAt: new Date().toISOString()
                }
            });

        } catch (error) {
            console.error('Error getting time-based stats:', error);
            return ctx.internalServerError('Failed to retrieve time-based statistics');
        }
    },

    // API thống kê top entities
    async getTopStats(ctx) {
        const { limit = 10 } = ctx.query;

        try {
            // Top patients theo patient_source
            const topPatientSources = await strapi.db.connection.raw(`
                SELECT 
                    ps.name as source_name,
                    COUNT(p.id) as patient_count
                FROM patients p
                LEFT JOIN patient_sources ps ON p.patient_source = ps.id
                GROUP BY p.patient_source, ps.name
                ORDER BY patient_count DESC
                LIMIT ?
            `, [limit]);

            // Top drugs theo stock
            const topDrugsByStock = await strapi.db.query('api::drug.drug').findMany({
                select: ['code', 'label', 'stock', 'unit'],
                orderBy: { stock: 'desc' },
                limit: parseInt(limit)
            });

            // Top drugs sắp hết hạn
            const expiringSoonDrugs = await strapi.db.query('api::drug.drug').findMany({
                select: ['code', 'label', 'endDate', 'stock'],
                where: { 
                    endDate: { 
                        $gte: new Date(),
                        $lt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                    }
                },
                orderBy: { endDate: 'asc' },
                limit: parseInt(limit)
            });

            return ctx.send({
                status: 0,
                message: 'Top statistics retrieved successfully',
                data: {
                    topPatientSources: topPatientSources[0] || [],
                    topDrugsByStock: topDrugsByStock,
                    expiringSoonDrugs: expiringSoonDrugs,
                    generatedAt: new Date().toISOString()
                }
            });

        } catch (error) {
            console.error('Error getting top stats:', error);
            return ctx.internalServerError('Failed to retrieve top statistics');
        }
    },

    // API thống kê health check
    async getSystemHealth(ctx) {
        try {
            const now = new Date();
            
            // Check database connectivity
            const dbCheck = await strapi.db.query('api::patient.patient').count();
            
            // Check expired drugs
            const expiredDrugs = await strapi.db.query('api::drug.drug').count({
                where: { endDate: { $lt: now } }
            });

            // Check users without patients
            const usersWithoutPatients = await strapi.db.connection.raw(`
                SELECT COUNT(*) as count
                FROM up_users u
                LEFT JOIN patients p ON u.id = p.user
                WHERE p.id IS NULL AND u.blocked = 0
            `);

            // System health score calculation
            let healthScore = 100;
            const issues = [];

            if (expiredDrugs > 0) {
                healthScore -= Math.min(expiredDrugs * 5, 30);
                issues.push(`${expiredDrugs} expired drugs found`);
            }

            const orphanUsers = usersWithoutPatients[0][0].count;
            if (orphanUsers > 0) {
                healthScore -= Math.min(orphanUsers * 2, 20);
                issues.push(`${orphanUsers} users without patient records`);
            }

            let healthStatus;
            if (healthScore >= 90) healthStatus = 'Excellent';
            else if (healthScore >= 70) healthStatus = 'Good';
            else if (healthScore >= 50) healthStatus = 'Fair';
            else healthStatus = 'Poor';

            return ctx.send({
                status: 0,
                message: 'System health check completed',
                data: {
                    healthScore: Math.max(healthScore, 0),
                    healthStatus: healthStatus,
                    issues: issues,
                    checks: {
                        databaseConnectivity: dbCheck !== undefined,
                        expiredDrugs: expiredDrugs,
                        orphanUsers: orphanUsers
                    },
                    recommendations: issues.length > 0 ? [
                        'Review and update expired drugs',
                        'Link users to patient records',
                        'Regular system maintenance recommended'
                    ] : ['System is running optimally'],
                    lastChecked: now.toISOString()
                }
            });

        } catch (error) {
            console.error('Error in system health check:', error);
            return ctx.internalServerError('Failed to perform system health check');
        }
    },

    // API thống kê chi tiết các dịch vụ
    async getServiceStats(ctx) {
        try {
            // Thống kê booking theo loại
            const bookingByType = await strapi.db.connection.raw(`
                SELECT
                    type,
                    COUNT(*) as count,
                    SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed
                FROM bookings
                GROUP BY type
                ORDER BY count DESC
            `);

            // Thống kê booking theo branch
            const bookingByBranch = await strapi.db.connection.raw(`
                SELECT
                    branch,
                    COUNT(*) as count,
                    SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed
                FROM bookings
                GROUP BY branch
                ORDER BY count DESC
            `);

            // Thống kê order theo status
            const orderByStatus = await strapi.db.connection.raw(`
                SELECT
                    status,
                    COUNT(*) as count,
                    SUM(total) as revenue
                FROM orders
                GROUP BY status
                ORDER BY count DESC
            `);

            // Thống kê conversation theo supporter
            const conversationBySupporter = await strapi.db.connection.raw(`
                SELECT
                    supporter,
                    COUNT(*) as count,
                    SUM(CASE WHEN waiting = 1 THEN 1 ELSE 0 END) as active
                FROM conversation_queues
                WHERE supporter IS NOT NULL
                GROUP BY supporter
                ORDER BY count DESC
            `);

            // Thống kê patient theo source
            const patientBySource = await strapi.db.connection.raw(`
                SELECT
                    ps.name as source_name,
                    COUNT(p.id) as count
                FROM patients p
                LEFT JOIN patient_sources ps ON p.patient_source = ps.id
                GROUP BY p.patient_source, ps.name
                ORDER BY count DESC
            `);

            // Thống kê article theo category
            const articleByCategory = await strapi.db.connection.raw(`
                SELECT
                    category,
                    COUNT(*) as count,
                    SUM(CASE WHEN published_at IS NOT NULL THEN 1 ELSE 0 END) as published
                FROM articles
                GROUP BY category
                ORDER BY count DESC
            `);

            // Thống kê package theo type
            const packageByType = await strapi.db.connection.raw(`
                SELECT
                    type,
                    COUNT(*) as count
                FROM packages
                GROUP BY type
                ORDER BY count DESC
            `);

            // Thống kê drug theo branch
            const drugByBranch = await strapi.db.connection.raw(`
                SELECT
                    branch,
                    COUNT(*) as count,
                    SUM(stock) as total_stock,
                    SUM(CASE WHEN end_date < NOW() THEN 1 ELSE 0 END) as expired
                FROM drugs
                WHERE branch IS NOT NULL
                GROUP BY branch
                ORDER BY count DESC
            `);

            return ctx.send({
                status: 0,
                message: 'Service statistics retrieved successfully',
                data: {
                    bookings: {
                        byType: bookingByType[0] || [],
                        byBranch: bookingByBranch[0] || []
                    },
                    orders: {
                        byStatus: orderByStatus[0] || []
                    },
                    conversations: {
                        bySupporter: conversationBySupporter[0] || []
                    },
                    patients: {
                        bySource: patientBySource[0] || []
                    },
                    content: {
                        articlesByCategory: articleByCategory[0] || [],
                        packagesByType: packageByType[0] || []
                    },
                    drugs: {
                        byBranch: drugByBranch[0] || []
                    },
                    generatedAt: new Date().toISOString()
                }
            });

        } catch (error) {
            console.error('Error getting service stats:', error);
            return ctx.internalServerError('Failed to retrieve service statistics');
        }
    },

    // API thống kê theo khoảng thời gian tùy chỉnh
    async getCustomPeriodStats(ctx) {
        const { startDate, endDate, entities = 'all' } = ctx.query;

        if (!startDate || !endDate) {
            return ctx.badRequest('Start date and end date are required');
        }

        try {
            const results = {};

            if (entities === 'all' || entities.includes('patients')) {
                results.patients = await strapi.db.connection.raw(`
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as count
                    FROM patients
                    WHERE DATE(created_at) BETWEEN ? AND ?
                    GROUP BY DATE(created_at)
                    ORDER BY date
                `, [startDate, endDate]);
            }

            if (entities === 'all' || entities.includes('bookings')) {
                results.bookings = await strapi.db.connection.raw(`
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as count,
                        SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed
                    FROM bookings
                    WHERE DATE(created_at) BETWEEN ? AND ?
                    GROUP BY DATE(created_at)
                    ORDER BY date
                `, [startDate, endDate]);
            }

            if (entities === 'all' || entities.includes('orders')) {
                results.orders = await strapi.db.connection.raw(`
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as count,
                        SUM(total) as revenue
                    FROM orders
                    WHERE DATE(created_at) BETWEEN ? AND ?
                    GROUP BY DATE(created_at)
                    ORDER BY date
                `, [startDate, endDate]);
            }

            return ctx.send({
                status: 0,
                message: 'Custom period statistics retrieved successfully',
                data: {
                    period: {
                        startDate: startDate,
                        endDate: endDate
                    },
                    entities: entities,
                    results: results,
                    generatedAt: new Date().toISOString()
                }
            });

        } catch (error) {
            console.error('Error getting custom period stats:', error);
            return ctx.internalServerError('Failed to retrieve custom period statistics');
        }
    }

}));
