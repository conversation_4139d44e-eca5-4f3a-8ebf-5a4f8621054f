{"kind": "collectionType", "collectionName": "chronic_services", "info": {"singularName": "chronic-service", "pluralName": "chronic-services", "displayName": "Chronic Service", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"label": {"type": "string"}, "Services": {"displayName": "Service", "type": "component", "repeatable": true, "component": "service.service"}, "price": {"type": "integer"}}}