"use strict";
const utils = require('@strapi/utils');
const moment = require("moment");
const { ApplicationError, ValidationError } = utils.errors;
const nodemailer = require("nodemailer");
const fetch = require('node-fetch')
const crypto = require("crypto");
const https = require("https");

/**
 *  order controller
 */

const { createCoreController } = require("@strapi/strapi").factories;

module.exports = createCoreController("api::order.order", ({ strapi }) => ({

  async updateP(ctx) {
    var services = await strapi
      .query('api::medical-service.medical-service')
      .findMany({
        where: {
          id: {
            $notNull: true,
          }
        }
      });

    services.forEach(async s => {
      const profile = await strapi.entityService.update('api::medical-service.medical-service', s.id, {
        data: {
          membership_discount: {
            medical_provider_percentage: 20,
            gold_percentage: 10,
            platinum_percentage: 30
          }
        }
      })
    })

    var bservices = await strapi
      .query('api::service-bundle.service-bundle')
      .findMany({
        where: {
          id: {
            $notNull: true,
          }
        }
      });

    bservices.forEach(async s => {
      const profile = await strapi.entityService.update('api::service-bundle.service-bundle', s.id, {
        data: {
          membership_discount: {
            medical_provider_percentage: 20,
            gold_percentage: 10,
            platinum_percentage: 30
          }
        }
      })
    })

    return services;
  },
  // wrap a core action, leaving core logic in place
  async count(ctx) {
    var { query } = ctx.request.body;
    return strapi.query("api::order.order").count({ where: query });
  },
  async getOrderDetailByCode(ctx) {
    const { code } = ctx.params;
    var order = await strapi.db.query('api::order.order').findOne({
      populate: {
        cart: {
          populate: {
            cart_lines: {
              populate: {
                product: true,
                service: true,
              }
            },
          }
        },
        medicines: {
          populate: {
            image: true,
          }
        },
      },
      where: {
        code
      }
    });

    return {
      order,
    };
  },
  async getOrderDetail(ctx) {
    if (!ctx.state.user) {
      throw new ApplicationError('You must be authenticated to reset your password');
    }

    const { id } = ctx.params;
    var product = await strapi.db.query('api::order.order').findOne({
      populate: {
        cart: {
          populate: {
            cart_lines: {
              populate: {
                product: true,
                service: true,
              }
            },
          }
        },
        medicines: {
          populate: {
            image: true,
          }
        },
      },
      where: {
        id
      }
    });

    return {
      product,
    };
  },

  async createMomoPaymentUrl(ctx) {
    if (!ctx.state.user) {
      throw new ApplicationError('You must be authenticated.');
    }

    const { id } = ctx.state.user;
    const user = await strapi
      .query('plugin::users-permissions.user')
      .findOne({ where: { id }, populate: { cart: true } });

    let cart = user.cart;
    if (!user.cart) {
      throw new ApplicationError("You don't have any item in cart.");
    }

    let result = await strapi.query('api::cart.cart').findOne({
      where: { id: cart.id },
      populate:
      {
        cart_lines:
        {
          populate: {
            product: true,
            service: true,
          }
        },
      }
    });

    let totalPrice = 0;
    result.cart_lines.forEach(element => {
      try {
        totalPrice = totalPrice + (element.product ? element.product.price : parseInt(element.service.price)) * element.quantity;
      } catch (e) {

      }
    });

    await strapi.plugins['users-permissions'].services.user.edit(id, { cart: null });
    
    const orderCode = generateCode("ORD");
    const partnerCode = "MOMOLRUU20240122"
    const accessKey = "FPUM9bVhLadKHN4d"
    const secretkey = "CT7ARSIKGdjqZ48LoseQZuGrgwXnHgoe"
    const amount = totalPrice;
    const redirectUrl = "http://echomedi.com/order_detail/?code=" + orderCode;
    const ipnUrl = 'https://api.echomedi.com/api/orders/approveMomoOrder';
    const requestType = ctx.request.body.requestType;
    const extraData = '';
    const requestId = partnerCode + new Date().getTime();
    const orderId = requestId;
    const orderInfo = "Pay with MoMo";
    const rawSignature = `accessKey=${accessKey}&amount=${amount}&extraData=${extraData}&ipnUrl=${ipnUrl}&orderId=${orderId}&orderInfo=${orderInfo}&partnerCode=${partnerCode}&redirectUrl=${redirectUrl}&requestId=${requestId}&requestType=${requestType}`;
    const signature = crypto
      .createHmac("sha256", secretkey)
      .update(rawSignature)
      .digest("hex");

    await strapi
      .query('api::order.order')
      .create({
        data: {
          paymentMethod: "momo",
          status: "draft",
          code: orderCode,
          cart: cart.id,
          users_permissions_user: id,
          publishedAt: new Date().toISOString(),
          total: totalPrice,
          num_of_prod: result.cart_lines ? result.cart_lines.length : 0,
          reference_id: requestId
        }
      });

    const requestBody = JSON.stringify({
      partnerCode,
      accessKey,
      requestId,
      amount,
      orderId,
      orderInfo,
      redirectUrl,
      ipnUrl,
      extraData,
      requestType,
      signature,
      lang: "en",
    });

    let res = await fetch('https://payment.momo.vn/v2/gateway/api/create', {
      method: 'POST',
      headers: {
        "Content-Type": "application/json",
        "Content-Length": Buffer.byteLength(requestBody),
      },
      body: requestBody
    }).then((response) => response.json());

    console.log('createMomoPaymentUrl', requestType, res);

    return res;
  },

  async createPaymentUrl(ctx) {
    if (!ctx.state.user) {
      throw new ApplicationError('You must be authenticated.');
    }

    const { id } = ctx.state.user;
    const user = await strapi
      .query('plugin::users-permissions.user')
      .findOne({ where: { id }, populate: { cart: true } });

    let cart = user.cart;
    if (!user.cart) {
      throw new ApplicationError("You don't have any item in cart.");
    }

    let result = await strapi.query('api::cart.cart').findOne({
      where: { id: cart.id },
      populate:
      {
        cart_lines:
        {
          populate: {
            product: true,
            service: true,
          }
        },
      }
    });

    let totalPrice = 0;
    result.cart_lines.forEach(element => {
      try {
        totalPrice = totalPrice + (element.product ? element.product.price : parseInt(element.service.price)) * element.quantity;
      } catch (e) {

      }
    });

    await strapi.plugins['users-permissions'].services.user.edit(id, { cart: null });

    var orderCode = generateCode("ORD");
    const req = ctx.request;

    var ipAddr = req.headers['x-forwarded-for'] ||
      req.connection?.remoteAddress ||
      req.socket?.remoteAddress ||
      req.connection?.socket?.remoteAddress;

    var secretKey = "KM349Y43X9G7VHI6NWZU9MCENQ0Q5IPK";
    var vnpUrl = "https://pay.vnpay.vn/vpcpay.html";
    var returnUrl = "http://echomedi.com/order_detail/?code=" + orderCode;

    var createDate = moment().format('YYYYMMDDhhmmss').toString();
    var amount = totalPrice;
    var bankCode = "";

    var orderType = "billpayment";
    var locale = req.body.language;
    if (locale === null || locale === '') {
      locale = 'vn';
    }
    var currCode = 'VND';
    var vnp_Params = {};
    vnp_Params['vnp_Version'] = '2.1.0';
    vnp_Params['vnp_Command'] = 'pay';
    vnp_Params['vnp_TmnCode'] = "ECHOMEDI";
    vnp_Params['vnp_Locale'] = "vn";
    vnp_Params['vnp_CurrCode'] = currCode;
    vnp_Params['vnp_TxnRef'] = orderCode;
    vnp_Params['vnp_OrderInfo'] = orderCode;
    vnp_Params['vnp_OrderType'] = orderType;
    vnp_Params['vnp_Amount'] = amount * 100;
    vnp_Params['vnp_ReturnUrl'] = returnUrl;
    vnp_Params['vnp_IpAddr'] = ipAddr;
    vnp_Params['vnp_CreateDate'] = createDate;
    if (bankCode !== null && bankCode !== '') {
      vnp_Params['vnp_BankCode'] = bankCode;
    }

    vnp_Params = sortObject(vnp_Params);

    var querystring = require('qs');
    var signData = querystring.stringify(vnp_Params, { encode: false });
    var crypto = require("crypto");
    var hmac = crypto.createHmac("sha512", secretKey);
    var signed = hmac.update(new Buffer(signData, 'utf-8')).digest("hex");
    vnp_Params['vnp_SecureHash'] = signed;
    vnpUrl += '?' + querystring.stringify(vnp_Params, { encode: false });

    await strapi
      .query('api::order.order')
      .create({
        data: {
          paymentMethod: "vnpay",
          status: "draft",
          code: orderCode,
          cart: cart.id,
          users_permissions_user: id,
          publishedAt: new Date().toISOString(),
          total: totalPrice,
          num_of_prod: result.cart_lines ? result.cart_lines.length : 0,
          vnp_payment_url_params: vnp_Params,
        }
      });

    try {

      let transporter = nodemailer.createTransport({
        host: "smtp.hostinger.com",
        port: 465,
        secure: true, // true for 465, false for other ports
        auth: {
          user: "<EMAIL>", // generated ethereal user
          pass: "1026Echomedi@123", // generated ethereal password
        },
      });

      await transporter.sendMail({
        from: '<<EMAIL>>', // sender address
        to: ctx.state.user.email, // list of receivers
        subject: "ECHO MEDI - Xác Nhận Thanh Toán - [Mã thanh toán]", // Subject line
        text: "Xin chào", // plain text body
        html: `
          <p>Xin chào [Tên khách hàng],</p>
          
          <p>Đầu tiên, ECHO MEDI cảm ơn bạn vì đã tin gửi sức khỏe toàn diện cho chúng tôi. </p>
          <p>Dưới đây là thông tin đơn hàng bạn vừa thanh toán qua website ECHO MEDI, bạn vui lòng kiểm tra lại các thông tin và liên hệ ngay với chúng tôi nếu có bất kỳ chỉnh sửa nào</p>
          <p>[Thông tin đơn hàng]</p>
          
          <p>Nếu không có bất kỳ vấn đề gì, đơn hàng sẽ được ghi nhận và chuyển tới các bước kế tiếp</p>
          <p>[Bước kế tiếp: liên hệ khám, dặn dò trước khám, etc.]</p>
          <p>Trong vài ngày tới, ECHO MEDI sẽ liên hệ với bạn để lên lịch hẹn khám/giao hàng. Bạn vui lòng để ý điện thoại/email/tin nhắn và phản hồi với chúng tôi nếu có thắc mắc hoặc vấn đề phát sinh nhé</p>

          <p>Cảm ơn bạn vì đã quan tâm đến sức khỏe của mình,</p>
          <p>ECHO MEDI</p>
      `, // html body
      });
    } catch (e) {
      console.log('ERror', e);
    }

    ctx.send({ url: vnpUrl, code: orderCode });
  },
  async updateOrder(ctx) {
    // const filter = utils.convertQueryParams(ctx.request.query);
    const params = ctx.request.query;

    var vnp_Params = ctx.request.query;

    var secureHash = vnp_Params['vnp_SecureHash'];

    delete vnp_Params['vnp_SecureHash'];
    delete vnp_Params['vnp_SecureHashType'];

    vnp_Params = sortObject(vnp_Params);

    var tmnCode = "ECHOMEDI";
    var secretKey = "KM349Y43X9G7VHI6NWZU9MCENQ0Q5IPK";

    var querystring = require('qs');
    var signData = querystring.stringify(vnp_Params, { encode: false });
    var crypto = require("crypto");
    var hmac = crypto.createHmac("sha512", secretKey);
    var signed = hmac.update(new Buffer(signData, 'utf-8')).digest("hex");

    const req = ctx.request;
    var ipAddr = req.headers['x-forwarded-for'] ||
      req.connection?.remoteAddress ||
      req.socket?.remoteAddress ||
      req.connection?.socket?.remoteAddress;

    params["ipAddr"] = ipAddr;

    const order = await strapi.query('api::order.order').findOne({
      where: {
        code: params.vnp_OrderInfo
      }
    });

    let result = null;

    if (!order) {
      result = { "Message": "Order not found", "RspCode": "01" }
    }
    else if (secureHash !== signed) {
      result = { "Message": "Invalid Signature", "RspCode": "97" }
    }
    else if (params.vnp_TxnRef != order.vnp_payment_url_params.vnp_TxnRef) {
      result = { "Message": "Order not found", "RspCode": "01" }
    }
    else if (params.vnp_Amount != order.vnp_payment_url_params.vnp_Amount) {
      result = { "Message": "Invalid amount", "RspCode": "04" }
    }
    else if (order.status == "ordered") {
      result = { "Message": "Order already confirmed", "RspCode": "02" }
    }

    params["response"] = result;

    await strapi.query('api::log.log').create({
      data: {
        message: "updateOrder",
        data: params,
      }
    });

    if (result != null) return result;

    let status = "";
    if (params.vnp_ResponseCode == "00" && params.vnp_TransactionStatus == "00") {
      status = "ordered";
    }

    if (params.vnp_OrderInfo.startsWith("ORD")) {
      await strapi.query('api::order.order').update({
        where: {
          code: params.vnp_OrderInfo
        },
        data: {
          status,
          vnp_payload: params
        }
      });
    } else {
      await strapi.query('api::booking.booking').update({
        where: {
          id: parseInt(params.vnp_OrderInfo)
        },
        data: {
          paid: true,
        }
      });
    }

    return { "Message": "Confirm Success", "RspCode": "00" };
  },
  async getOrderHistory(ctx) {
    const { id } = ctx.state.user;
    let orders = await strapi.query('api::order.order').findMany({
      where: { users_permissions_user: id },
      populate:
      {
        cart_lines:
        {
          populate: {
            product: true,
            service: true,
          }
        },
      }
    });

    return { orders };
  },

  async approvePaypalOrder(ctx) {
    await fetch('https://api-m.paypal.com/v2/checkout/orders/' + ctx.request.body.orderID, {
      method: 'GET',
      headers: {
        'Authorization': 'Basic ' + btoa('AX6JrQc0qGHNZjcq7hSLAYgz9B4uMOvcA3HGl4PXYZ7gR7IZLpcoyZSVug5zjZzF5nUuMADM9AUU3sE1:EBLWcfipT94H4E_cVk3fr_kCM3x2Ft3e_oq9SouoxSRYTNX__AihuAok-yh-Ejeq42tJl0MbSyCaV4EN')
      }
    })
      .then((response) => response.json());

    const order = await strapi.query('api::order.order').update({
      where: {
        reference_id: ctx.request.body.orderID
      },
      data: {
        status: "done",
      }
    });



    return order;
  },

  async approveMomoOrder(ctx) {
    await strapi.query('api::order.order').update({
      where: {
        reference_id: ctx.request.body.requestId
      },
      data: {
        status: ctx.request.body.resultCode == 0 ? "done" : "canceled",
      }
    });

    await strapi.query('api::log.log').create({
      data: {
        message: "updateOrder",
        data: ctx.request.body,
      }
    });

    ctx.send({}, 204);
  },

  async createPaypalOrder(ctx) {
    let totalPrice = ctx.request.body.totalPrice;
    var orderCode = generateCode("ORD");

    let token = await fetch('https://api-m.paypal.com/v1/oauth2/token', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Accept-Language': 'en_US',
        'Authorization': 'Basic ' + btoa('AX6JrQc0qGHNZjcq7hSLAYgz9B4uMOvcA3HGl4PXYZ7gR7IZLpcoyZSVug5zjZzF5nUuMADM9AUU3sE1:EBLWcfipT94H4E_cVk3fr_kCM3x2Ft3e_oq9SouoxSRYTNX__AihuAok-yh-Ejeq42tJl0MbSyCaV4EN')
      },
      body: new URLSearchParams({
        'grant_type': 'client_credentials'
      })
    }).then((response) => response.json());

    let orderData = await fetch("https://api-m.paypal.com/v2/checkout/orders", {
      method: "POST",
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token.access_token}`,
      },
      body: JSON.stringify({
        "purchase_units": [
          {
            "amount": {
              "currency_code": "USD",
              "value": (totalPrice / 24000).toFixed(2),
            },
            "reference_id": "d9f80740-38f0-11e8-b467-0ed5f89f718b"
          }
        ],
        "intent": "CAPTURE",
        "payment_source": {
          "paypal": {
            "experience_context": {
              "payment_method_preference": "IMMEDIATE_PAYMENT_REQUIRED",
              "payment_method_selected": "PAYPAL",
              "brand_name": "ECHO MEDI COMPANY LIMITED",
              "locale": "en-US",
              "landing_page": "LOGIN",
              "user_action": "PAY_NOW",
              "return_url": "https://echomedi.com",
              "cancel_url": "https://echomedi.com"
            }
          }
        }
      })
    })
      .then((response) => response.json());

    await strapi
      .query('api::order.order')
      .create({
        data: {
          publishedAt: new Date().toISOString(),
          total: totalPrice,
          products: ctx.request.body,
          code: orderCode,
          status: "draft",
          reference_id: orderData.id,
        }
      });

    return orderData;
  },

  async createOrder(ctx) {
    if (!ctx.state.user) {
      throw new ApplicationError('You must be authenticated to reset your password');
    }

    const { id } = ctx.state.user;
    const user = await strapi
      .query('plugin::users-permissions.user')
      .findOne({ where: { id }, populate: { cart: true } });

    let cart = user.cart;
    if (!user.cart) {
      throw new ApplicationError("You don't have any item in cart.");
    }

    let result = await strapi.query('api::cart.cart').findOne({
      where: { id: cart.id },
      populate:
      {
        cart_lines:
        {
          populate: {
            product: true,
            service: true,
          }
        },
      }
    });

    let totalPrice = 0;
    result.cart_lines.forEach(element => {
      try {
        totalPrice = totalPrice + element.product ? element.product.price : parseInt(element.service.price);
      } catch (e) {

      }
    });

    let order = await strapi
      .query('api::order.order')
      .create({
        data: {
          cart: cart.id,
          users_permissions_user: id,
          publishedAt: new Date().toISOString(),
          total: totalPrice,
          num_of_prod: result.cart_lines ? result.cart_lines.length : 0,
        }
      });

    await strapi.plugins['users-permissions'].services.user.edit(id, { cart: null });

    return order;
  }
}));


function sortObject(obj) {
  var sorted = {};
  var str = [];
  var key;
  for (key in obj) {
    if (obj.hasOwnProperty(key)) {
      str.push(encodeURIComponent(key));
    }
  }
  str.sort();
  for (key = 0; key < str.length; key++) {
    sorted[str[key]] = encodeURIComponent(obj[str[key]]).replace(/%20/g, "+");
  }
  return sorted;
}

function generateCode(prefix) {
  const CODE_LENGTH = 6;
  var result = "";
  var characters =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  var charactersLength = characters.length;
  for (var i = 0; i < CODE_LENGTH; i++) {
    result += characters.charAt(
      Math.floor(Math.random() * charactersLength)
    );
  }
  return (prefix + result).toLocaleUpperCase();
}