{"kind": "collectionType", "collectionName": "orders", "info": {"singularName": "order", "pluralName": "orders", "displayName": "Order", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"orderedDate": {"type": "datetime"}, "contactReceiver": {"type": "string"}, "contactPhoneNumber": {"type": "string"}, "contactEmail": {"type": "string"}, "contactAddress": {"type": "json"}, "subTotal": {"type": "biginteger"}, "promotion": {"type": "biginteger"}, "shippingFee": {"type": "biginteger"}, "tax": {"type": "integer"}, "total": {"type": "biginteger"}, "products": {"type": "json"}, "image": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "paymentMethod": {"type": "string"}, "code": {"type": "string"}, "status": {"type": "enumeration", "enum": ["draft", "ordered", "done", "canceled"], "default": "ordered"}, "dynamicLink": {"type": "string"}, "users_permissions_user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "cart": {"type": "relation", "relation": "oneToOne", "target": "api::cart.cart", "inversedBy": "order"}, "cart1": {"type": "relation", "relation": "oneToOne", "target": "api::cart.cart"}, "num_of_prod": {"type": "integer"}, "vnp_payload": {"type": "json"}, "vnp_payment_url_params": {"type": "json"}, "reference_id": {"type": "uid"}}}