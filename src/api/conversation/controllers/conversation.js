'use strict';

/**
 * conversation controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::conversation.conversation',
({ strapi }) => ({
    async create(ctx) {
        const conversation = await strapi.query("api::conversation.conversation").create({
            data: {
              ...ctx.request.body,
              publishedAt: new Date(),
            }
        });
        return conversation;
    }}));
