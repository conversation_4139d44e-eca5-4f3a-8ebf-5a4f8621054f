{"kind": "collectionType", "collectionName": "conversations", "info": {"singularName": "conversation", "pluralName": "conversations", "displayName": "Conversation", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"first_person": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "second_person": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "data": {"type": "json"}, "conversation_queue": {"type": "relation", "relation": "oneToOne", "target": "api::conversation-queue.conversation-queue", "mappedBy": "conversation"}, "latest_message": {"type": "string"}}}