'use strict';

/**
 * list controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::list.list', ({ strapi }) => ({
  async taskSwitch(ctx) {

    const {
      home,
      foreign,
    } = ctx.request.body;

    await strapi.query("api::list.list")
      .update({
        where: { uid: home.uid },
        data: { tasks: home.taskIDs }
      });

    await strapi.query("api::list.list")
      .update({
        where: { uid: foreign.uid },
        data: { tasks: foreign.taskIDs }
      });

    return { code: 200 };
  },
  // wrap a core action, leaving core logic in place
  async updateProperty(ctx) {
    const {
      id,
      property,
      data
    } = ctx.request.body;

    var task = await strapi.query("api::list.list")
      .update({
        where: { id: id },
        data: { [property]: data }
      });

    return task;
  }
}));
