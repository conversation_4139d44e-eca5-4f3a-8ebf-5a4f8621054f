{"kind": "collectionType", "collectionName": "lists", "info": {"singularName": "list", "pluralName": "lists", "displayName": "List", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"uid": {"type": "uid"}, "title": {"type": "string"}, "board": {"type": "relation", "relation": "manyToOne", "target": "api::board.board", "inversedBy": "lists"}, "tasks": {"type": "relation", "relation": "oneToMany", "target": "api::task.task", "mappedBy": "list"}, "taskIds": {"type": "json"}, "approvalType": {"type": "string"}, "defaultAssigments": {"type": "json"}}}