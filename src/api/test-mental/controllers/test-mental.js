'use strict';

const { createCoreController } = require('@strapi/strapi').factories;
module.exports = createCoreController('api::test-mental.test-mental', ({ strapi }) => ({
    async createTestResult(ctx) {

        if (!ctx.state.user) {
            return ctx.unauthorized('You must be authenticated.');
        }

        const { id } = ctx.state.user;
        const { test_name, test_url } = ctx.request.body;

        if (!test_name || !test_url) {
            return ctx.badRequest('Missing test_name or test_url');
        }

        const result = await strapi.entityService.create('api::test-mental.test-mental', {
            data: {
                test_name,
                test_url,
                users_permissions_user: id,
            },
        });

        return { success: true, data: result };
    },

    async getTestResults(ctx) {
        
        if (!ctx.state.user) {
            return ctx.unauthorized('You must be authenticated.');
        }
        const { id } = ctx.state.user;
        const results = await strapi.entityService.findMany('api::test-mental.test-mental', {
            filters: {
                users_permissions_user: id,
            },
            populate: ['users_permissions_user'],
        });

        return { success: true, data: results };
    },
}));

