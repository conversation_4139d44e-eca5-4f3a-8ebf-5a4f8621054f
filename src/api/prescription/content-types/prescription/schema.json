{"kind": "collectionType", "collectionName": "prescriptions", "info": {"singularName": "prescription", "pluralName": "prescriptions", "displayName": "Prescription", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"medicalRecord": {"type": "relation", "relation": "oneToOne", "target": "api::medical-record.medical-record", "inversedBy": "prescription"}, "message": {"type": "text"}, "reExaminationDate": {"type": "datetime"}, "Drugs": {"type": "component", "repeatable": true, "component": "prescription-drug.prescription-drug"}, "additional_message": {"type": "string"}, "additional_drugs": {"type": "component", "repeatable": true, "component": "prescription-drug.prescription-drug"}, "medical_record": {"type": "relation", "relation": "manyToOne", "target": "api::medical-record.medical-record", "inversedBy": "prescriptions"}, "uid": {"type": "uid"}, "total_price": {"type": "integer"}}}