"use strict";

/**
 *  booking controller
 */

const { createCoreController } = require("@strapi/strapi").factories;
var admin = require("firebase-admin");
const AWS = require('aws-sdk');
const nodemailer = require("nodemailer");
const fs = require('fs');
const dayjs = require("dayjs");

const getBranchAddress = (branch, locale) => {
  switch (branch) {
    case "q7":
      if (locale == "vi") return "1026 Nguyễn <PERSON>, P. T<PERSON>, Quận 7, TP.HCM";
      else return "1026 <PERSON><PERSON><PERSON>, Tan Phong Ward, District 7, HCMC";
      break;
    case "q2":
      if (locale == "vi") return "46 Nguyễn Thị Đ<PERSON>nh, P.<PERSON>, TP.Thủ Đức, TP.HCM";
      else return "46 <PERSON><PERSON><PERSON>, An Phu Ward, Thu Duc City, HCMC";
      break;
    case "bd":
      if (locale == "vi") return "Canary Plaza, Căn 0102, Số 5, <PERSON><PERSON><PERSON>, P<PERSON>, TP. Thuậ<PERSON> An";
      else return "Canary Plaza, #0102, 5 Binh Duong Highway, Binh Hoa Ward, Thuan An City";
      break;
  }
  return "";
}

module.exports = createCoreController("api::booking.booking", ({ strapi }) => ({
  // wrap a core action, leaving core logic in place
  async count(ctx) {
    var { query } = ctx.request.body;
    return strapi.query("api::booking.booking").count({ where: query });
  },

  async getBookingWithRange(ctx) {
    let where = {
      $and: [
        {
          bookingDate: {
            $gte: new Date(ctx.request.body.data.startDate),
            $lte: new Date(ctx.request.body.data.endDate),
          },
        },
        {
          branch: { $in: [ctx.request.body.data.branch, "all"] }
        }
      ]
    };


    if (ctx.request.body.data.status) {
      where.$and.push({
        status: {
          $in: ctx.request.body.data.status,
        }
      });
    }

    if ("dontShowOnCalendar" in ctx.request.body.data) {
      where.$and.push({ dontShowOnCalendar: ctx.request.body.data.dontShowOnCalendar });
    }

    let bookings = await strapi.query("api::booking.booking").findMany({
      where,
      populate: {
        patient: true,
        medical_record: {
          populate: {
            doctor_in_charge: true,
            cc_in_charge: true,
            nurse_in_charge: true,
          }
        },
      },
      orderBy: {
        bookingDate: "asc",
      }
    });


    return { bookings };
  },
  async updateBooking(ctx) {

    let booking = await strapi.query("api::booking.booking").update({
      where: {
        id: ctx.request.body.data.id,
      },
      data: {
        ...ctx.request.body.data,
        publishedAt: new Date(),
      }
    });

    return { booking };
  },

  async cancelBooking(ctx) {
    let booking = await strapi.query("api::booking.booking").update({
      where: {
        id: ctx.request.body.id,
      },
      data: {
        status: "cancelled",
      }
    });

    return { booking }
  },

  async createBooking(ctx) {

    let patient, user;
    const { id } = ctx.state.user;
    if (ctx.request.body.data.patient) {
      patient = await strapi.query("api::patient.patient").findOne({
        where: { id: ctx.request.body.data.patient }
      });
    }

    if (!patient && id) {
      user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { id }, populate: {
            patient: true
          }
        });
      patient = user.patient;
    }

    if (ctx.request.body.data.branch == "bd") {
      ctx.request.body.data.branch = "binhduong";
    }

    if (ctx.request.body.data.createNewPatient) {
      patient = await strapi.query("api::patient.patient").create({
        data: {
          ...ctx.request.body.data,
          full_name_i: removeVietnameseTones(ctx.request.body.data.full_name),
          publishedAt: new Date(),
          source: 'web'
        }
      });
    }

    let booking = await strapi.query("api::booking.booking").create({
      data: {
        ...ctx.request.body.data,
        patient: patient?.id ?? user?.patient?.id,
        publishedAt: new Date(),
      }
    });

    if (ctx.request.body.data.notify) {

      try {
        const msg2 = {
          "topic": "doctor",
          "notification": {
            "title": "ECHO MEDI: Lịch đặt hẹn mới",
            "body": ctx.request.body.data?.full_name ?? patient.full_name,
          },
          apns: {
            payload: {
              aps: {
                'mutable-content': 1,
                'content-available': 1
              }
            }
          },
          data: {
            type: "new_booking",
            booking_id: booking.id.toString(),
            topic: "doctor",
            redirect_url: "https://admin.echomedi.com/bookings"
          },
          webpush: {
            fcmOptions: {
              link: "https://admin.echomedi.com/bookings"
            }
          }
        };


        admin.messaging().send(msg2)
          .then((response) => {
            console.log('sending msg to topic', response)
          })
          .catch((error) => {
            console.log('error when send fcm msg', error)
          });

      } catch (e) {
        console.log('Error when notify');
      }

      let admins = await strapi.query("plugin::users-permissions.user").findMany({
        where: {
          $or: [
            {
              role: {
                type: "care_concierge"
              }
            },
            {
              username: "<EMAIL>"
            }
          ]
        },
      });

      let tokens = admins.map(a => a.fcmToken);
      let uniqTokens = [...new Set(tokens)];
      uniqTokens.forEach(t => {
        if (!!t) {
          const msg = {
            "token": t,
            data: {
              type: "new_booking",
              "title": "Lịch đặt hẹn mới",
              "body": ctx.request.body.data?.full_name ?? patient.full_name,
              "topic": "doctor",
              "redirect_url": "https://admin.echomedi.com/bookings/" + booking.id,
              "url": "https://admin.echomedi.com/bookings",
            },
            apns: {
              payload: {
                aps: {
                  'mutable-content': 1,
                  'content-available': 1
                }
              }
            },
          };
          admin.messaging().send(msg)
            .then((response) => {
              console.log('successfully send to token ' + t);
            })
            .catch((error) => {
              console.log('error when send fcm msg ' + t, error)
            });
        }
      })

      AWS.config.update({
        accessKeyId: process.env.AWS_SES_KEY,
        secretAccessKey: process.env.AWS_SES_SECRET,
        region: "us-east-1",
      });

      let transporter = nodemailer.createTransport({
        SES: new AWS.SES({
          apiVersion: '2010-12-01'
        })
      });

      let html = fs.readFileSync(`${__dirname}/my-new-email_2023-06-13T053648.737309/my-new-email.html`, 'utf8');

      html = html.replaceAll("[FULL_NAME]", patient.full_name);
      html = html.replaceAll("[PHONE_NUMBER]", patient.phone);
      html = html.replaceAll("[ADDRESS]", ctx.request.body.data.contactAddress);
      html = html.replaceAll("[EMAIL]", patient.email);
      html = html.replaceAll("[DATE]", dayjs(ctx.request.body.data.bookingDate).add(7, 'hour').utc().format("H:mm DD-MM-YYYY"));
      html = html.replaceAll("[BRANCH_ADDRESS]", getBranchAddress(ctx.request.body.data.branch, "vi"));
      html = html.replaceAll("[NOTE]", ctx.request.body.data.note);

      let attachments = [];
      attachments.push(
        {
          filename: 'Echo_Medi9056.JPG',
          path:
            __dirname + '/my-new-email_2023-06-13T053648.737309/images/Echo_Medi9056.JPG',
          cid: 'Echo_Medi9056.JPG'
        },
        {
          filename: 'Echo_Medi9019.JPG',
          path:
            __dirname + '/my-new-email_2023-06-13T053648.737309/images/Echo_Medi9019.JPG',
          cid: 'Echo_Medi9019.JPG'
        },
        {
          filename: 'Echo_Medi9075.JPG',
          path:
            __dirname + '/my-new-email_2023-06-13T053648.737309/images/Echo_Medi9075.JPG',
          cid: 'Echo_Medi9075.JPG'
        },
        {
          filename: 'Echo_Medi9174.JPG',
          path:
            __dirname + '/my-new-email_2023-06-13T053648.737309/images/Echo_Medi9174.JPG',
          cid: 'Echo_Medi9174.JPG'
        },
        {
          filename: 'App_Store_Badge_US_Black.png',
          path:
            __dirname + '/my-new-email_2023-06-13T053648.737309/images/App_Store_Badge_US_Black.png',
          cid: 'App_Store_Badge_US_Black.png'
        },
        {
          filename: 'Google_Play_Badge_US.png',
          path:
            __dirname + '/my-new-email_2023-06-13T053648.737309/images/Google_Play_Badge_US.png',
          cid: 'Google_Play_Badge_US.png'
        },
        {
          filename: 'download_009f376226_1.png',
          path:
            __dirname + '/my-new-email_2023-06-13T053648.737309/images/download_009f376226_1.png',
          cid: 'download_009f376226_1.png'
        }
      )

      try {
        transporter.sendMail({
          from: '<EMAIL>',//  emailTemplate.defaultSendFromEmail,
          to: ctx.request.body.data.email ?? patient.email,
          subject: 'ECHO MEDI- Lịch đặt hẹn',// emailTemplate.label,
          html: html,
          attachments: attachments,
        }, (err, info) => {
          console.log("sendEmail err", err);
          console.log("sendEmail", info);
        });
      } catch (e) {
        console.log('Failed to send email booking notify', e);
      }
    }

    return { booking };
  },

  async getAppointments(ctx) {
    let bookings = await strapi.query('api::booking.booking').findMany({
      where: { patient: ctx.request.body.patient },
      populate: { patient: true }
    });

    return bookings;
  },

  async getBookings(ctx) {
    return { ok: true };
  },

  async getAppointmentsV2(ctx) {
    const { id } = ctx.state.user;
    const user = await strapi
      .query("plugin::users-permissions.user")
      .findOne({
        where: { id }, populate: {
          patient: true
        }
      });

    let bookings = await strapi.query('api::booking.booking').findMany({
      where: { patient: user.patient.id },
    });

    return bookings;
  },

  async createBookingFromWeb(ctx) {

    let patient = await strapi.query("api::patient.patient").findOne({
      where: {
        phone: ctx.request.body.data.phone,
      }
    });

    if (patient == null) {
      patient = await strapi.query("api::patient.patient").create({
        data: {
          ...ctx.request.body.data,
          publishedAt: new Date(),
          source: 'web'
        }
      });
    }

    let booking = await strapi.query("api::booking.booking").create({
      data: {
        ...ctx.request.body.data,
        status: "scheduled",
        type: "at_clinic",
        patient: patient.id,
        publishedAt: new Date(),
        dontShowOnCalendar: false,
      }
    });

    try {
      const msg2 = {
        "topic": "doctor",
        "notification": {
          "title": "ECHO MEDI: Lịch đặt hẹn mới",
          "body": ctx.request.body.data.full_name ?? patient.full_name,
        },
        apns: {
          payload: {
            aps: {
              'mutable-content': 1,
              'content-available': 1
            }
          }
        },
        data: {
          type: "new_booking",
          booking_id: booking.id.toString(),
          topic: "doctor",
          redirect_url: "https://admin.echomedi.com/bookings"
        },
        webpush: {
          fcmOptions: {
            link: "https://admin.echomedi.com/bookings"
          }
        }
      };

      admin.messaging().send(msg2)
        .then((response) => {
          console.log('sending msg to topic', response)
        })
        .catch((error) => {
          console.log('error when send fcm msg', error)
        });

      let admins = await strapi.query("plugin::users-permissions.user").findMany({
        where: {
          $or: [
            {
              role: {
                type: "care_concierge"
              }
            },
            {
              username: "<EMAIL>"
            }
          ]
        },
      });

      let tokens = admins.map(a => a.fcmToken);
      let uniqTokens = [...new Set(tokens)];
      uniqTokens.forEach(t => {
        if (!!t) {
          const msg = {
            "token": t,
            data: {
              type: "new_booking",
              "title": "Lịch đặt hẹn mới",
              "body": ctx.request.body.data.full_name ?? patient.full_name,
              "topic": "doctor",
              "redirect_url": "https://admin.echomedi.com/bookings/" + booking.id,
              "url": "https://admin.echomedi.com/bookings",
            },
            apns: {
              payload: {
                aps: {
                  'mutable-content': 1,
                  'content-available': 1
                }
              }
            },
          };
          admin.messaging().send(msg)
            .then((response) => {
            })
            .catch((error) => {
              console.log('error when send fcm msg', error)
            });
        }
      })


    } catch (e) {
      console.log('exception', e)
    }

    AWS.config.update({
      accessKeyId: process.env.AWS_SES_KEY,
      secretAccessKey: process.env.AWS_SES_SECRET,
      region: "us-east-1",
    });

    // create Nodemailer SES transporter
    let transporter = nodemailer.createTransport({
      SES: new AWS.SES({
        apiVersion: '2010-12-01'
      })
    });

    let html = fs.readFileSync(`${__dirname}/my-new-email_2023-06-13T053648.737309/my-new-email.html`, 'utf8');

    html = html.replaceAll("[FULL_NAME]", ctx.request.body.data.full_name ?? patient.full_name);
    html = html.replaceAll("[PHONE_NUMBER]", ctx.request.body.data.phone);
    html = html.replaceAll("[ADDRESS]", ctx.request.body.data.contactAddress);
    html = html.replaceAll("[EMAIL]", ctx.request.body.data.email ?? patient.email);
    html = html.replaceAll("[DATE]", dayjs(ctx.request.body.data.bookingDate).add(7, 'hour').utc().format("H:mm DD-MM-YYYY"));
    html = html.replaceAll("[BRANCH_ADDRESS]", getBranchAddress(ctx.request.body.data.branch, "vi"));

    let attachments = [];
    attachments.push(
      {
        filename: 'Echo_Medi9056.JPG',
        path:
          __dirname + '/my-new-email_2023-06-13T053648.737309/images/Echo_Medi9056.JPG',
        cid: 'Echo_Medi9056.JPG'
      },
      {
        filename: 'Echo_Medi9019.JPG',
        path:
          __dirname + '/my-new-email_2023-06-13T053648.737309/images/Echo_Medi9019.JPG',
        cid: 'Echo_Medi9019.JPG'
      },
      {
        filename: 'Echo_Medi9075.JPG',
        path:
          __dirname + '/my-new-email_2023-06-13T053648.737309/images/Echo_Medi9075.JPG',
        cid: 'Echo_Medi9075.JPG'
      },
      {
        filename: 'Echo_Medi9174.JPG',
        path:
          __dirname + '/my-new-email_2023-06-13T053648.737309/images/Echo_Medi9174.JPG',
        cid: 'Echo_Medi9174.JPG'
      },
      {
        filename: 'App_Store_Badge_US_Black.png',
        path:
          __dirname + '/my-new-email_2023-06-13T053648.737309/images/App_Store_Badge_US_Black.png',
        cid: 'App_Store_Badge_US_Black.png'
      },
      {
        filename: 'Google_Play_Badge_US.png',
        path:
          __dirname + '/my-new-email_2023-06-13T053648.737309/images/Google_Play_Badge_US.png',
        cid: 'Google_Play_Badge_US.png'
      },
      {
        filename: 'download_009f376226_1.png',
        path:
          __dirname + '/my-new-email_2023-06-13T053648.737309/images/download_009f376226_1.png',
        cid: 'download_009f376226_1.png'
      }
    )

    try {
      transporter.sendMail({
        from: '<EMAIL>',//  emailTemplate.defaultSendFromEmail,
        to: ctx.request.body.data.email ?? patient.email,
        subject: 'ECHO MEDI- Lịch đặt hẹn',// emailTemplate.label,
        html: html,
        attachments: attachments,
      }, (err, info) => {
        console.log("sendEmail err", err);
        console.log("sendEmail", info);
      });
    } catch (e) {
      console.log('Failed to send email booking notify', e);
    }

    return { booking };
  },

  async updateStatusBooking(ctx) {
    let booking = await strapi.query("api::booking.booking").update({
      where: {
        id: ctx.request.body.id,
      },
      data: {
        status: ctx.request.body.status,
      }
    });

    return { booking }
  },

  async exportInvoice(ctx) {
    let invoice = await strapi.query("api::invoice.invoice").create({
      data: {
        booking: ctx.request.body.id,
      }
    });

    return { invoice }
  }
}));



function removeVietnameseTones(str) {
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a");
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e");
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i");
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o");
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u");
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y");
  str = str.replace(/đ/g, "d");
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A");
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E");
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I");
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O");
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U");
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y");
  str = str.replace(/Đ/g, "D");
  // Some system encode vietnamese combining accent as individual utf-8 characters
  // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, ""); // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, ""); // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
  // Remove extra spaces
  // Bỏ các khoảng trắng liền nhau
  str = str.replace(/ + /g, " ");
  str = str.trim();
  // Remove punctuations
  // Bỏ dấu câu, kí tự đặc biệt
  str = str.replace(/!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g, " ");
  return str;
}