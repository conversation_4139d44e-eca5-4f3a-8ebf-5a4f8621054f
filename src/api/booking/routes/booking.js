'use strict';

/**
 * booking router.
 */

const { createCoreRouter } = require('@strapi/strapi').factories;
const defaultRouter = createCoreRouter("api::booking.booking");

const customRouter = (innerRouter, extraRoutes = []) => {
    let routes;
    return {
        get prefix() {
            return innerRouter.prefix;
        },
        get routes() {
            if (!routes) routes = innerRouter.routes.concat(extraRoutes);
            return routes;
        },
    };
};

const myExtraRoutes = [
    {
        method: "POST",
        path: "/bookings/count",
        handler: "booking.count",
        config: {
            description: "Count bookings",
            policies: [],
            prefix: "",
        },
    },
    {
        method: "POST",
        path: "/bookings/createBooking",
        handler: "booking.createBooking",
        config: {
            policies: [],
            prefix: "",
        },
    },
    {
        method: "POST",
        path: "/bookings/createBookingFromWeb",
        handler: "booking.createBookingFromWeb",
        config: {
            policies: [],
            prefix: "",
        },
    },
    {
        method: "POST",
        path: "/bookings/updateBooking",
        handler: "booking.updateBooking",
        config: {
            policies: [],
            prefix: "",
        },
    },
    {
        method: "POST",
        path: "/bookings/getBookingWithRange",
        handler: "booking.getBookingWithRange",
        config: {
            policies: [],
            prefix: "",
        },
    },
    {
        method: "POST",
        path: "/bookings/updateStatusBooking",
        handler: "booking.updateStatusBooking",
        config: {
            policies: [],
            prefix: "",
        },
    },
    {
        method: "POST",
        path: "/bookings/exportInvoice",
        handler: "booking.exportInvoice",
        config: {
            policies: [],
            prefix: "",
        },
    },
    {
        method: "POST",
        path: "/bookings/getAppointments",
        handler: "booking.getAppointments",
        config: {
            policies: [],
            prefix: "",
        },
    },
    {
        method: "GET",
        path: "/bookings/getBookings",
        handler: "booking.getBookings",
    },
    {
        method: "POST",
        path: "/bookings/getAppointmentsV2",
        handler: "booking.getAppointmentsV2",
    },
    {
        method: "POST",
        path: "/bookings/cancelBooking",
        handler: "booking.cancelBooking",
    },
];


module.exports = customRouter(defaultRouter, myExtraRoutes);