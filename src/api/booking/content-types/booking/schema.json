{"kind": "collectionType", "collectionName": "bookings", "info": {"singularName": "booking", "pluralName": "bookings", "displayName": "Booking", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"timeSession": {"type": "string"}, "contactReceiver": {"type": "string"}, "contactPhoneNumber": {"type": "string"}, "contactEmail": {"type": "string"}, "contactAddress": {"type": "json"}, "status": {"type": "enumeration", "enum": ["scheduled", "confirmed", "finished", "cancelled", "postpone", "waiting"]}, "note": {"type": "text"}, "callToRemind": {"type": "boolean", "default": false}, "bookingDate": {"type": "datetime"}, "code": {"type": "string"}, "createdByAdmin": {"type": "boolean"}, "scheduleTreatmentTimes": {"type": "integer"}, "treatmentTime": {"type": "integer"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "bookings"}, "patient": {"type": "relation", "relation": "oneToOne", "target": "api::patient.patient"}, "medical_record": {"type": "relation", "relation": "oneToOne", "target": "api::medical-record.medical-record", "mappedBy": "booking"}, "branch": {"type": "enumeration", "enum": ["q7", "q2", "<PERSON><PERSON><PERSON><PERSON>", "bd", "all"]}, "dontShowOnCalendar": {"type": "boolean"}, "membership": {"type": "string"}, "latitude": {"type": "decimal"}, "longitude": {"type": "decimal"}, "type": {"type": "enumeration", "enum": ["at_clinic", "home_visit", "telemedicine"]}, "contactFullName": {"type": "string"}, "cc_note": {"type": "string"}, "paid": {"type": "boolean"}}}