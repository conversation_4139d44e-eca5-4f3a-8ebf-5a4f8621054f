module.exports = {
    routes: [
      {
        method: "POST",
        path: "/patient/updatePatient",
        handler: "patient.updatePatient",
      },
      {
        method: "GET",
        path: "/patient/findByPhoneNumber/:phone",
        handler: "patient.findByPhoneNumber",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "GET",
        path: "/patient/checkUserExistByPhone/:phone",
        handler: "patient.checkUserExistByPhone",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "GET",
        path: "/patient/getRelationship/:id",
        handler: "patient.getRelationship",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/patient/createUserFromPatient",
        handler: "patient.createUserFromPatient",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/patient/updatePatientRelationship/:id",
        handler: "patient.updatePatientRelationship",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/patient/createPatientAndUser",
        handler: "patient.createPatientAndUser",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "GET",
        path: "/patient/setFullNameI",
        handler: "patient.setFullNameI",
      },
      {
        method: "POST",
        path: "/patient/createnootp",
        handler: "patient.createOTP",
        config: {
          policies: [],
          prefix: false,
        },
      }
    ]
}