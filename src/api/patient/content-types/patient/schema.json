{"kind": "collectionType", "collectionName": "patients", "info": {"singularName": "patient", "pluralName": "patients", "displayName": "Patient", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"full_name": {"type": "string"}, "birthday": {"type": "datetime"}, "gender": {"type": "string"}, "job": {"type": "string"}, "address": {"type": "json"}, "phone": {"type": "string"}, "email": {"type": "string"}, "full_name_i": {"type": "string"}, "membership": {"type": "string"}, "uid": {"type": "uid"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "inversedBy": "patient"}, "relative_phone": {"type": "string"}, "relationships": {"type": "component", "repeatable": true, "component": "relationship.relationship"}, "internal": {"type": "string"}, "start_date_membership": {"type": "date"}, "membership_profile_file": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "source": {"type": "enumeration", "enum": ["app", "web", "app_be", "other"], "default": "other"}, "patient_source": {"type": "relation", "relation": "oneToOne", "target": "api::patient-source.patient-source"}, "discount": {"type": "json"}, "currentHealth": {"type": "json"}, "remaining_services": {"type": "component", "repeatable": true, "component": "service.service"}, "rem_services": {"type": "json"}, "address_cccd": {"type": "string"}, "cccd": {"type": "string"}}}