'use strict';

/**
 * patient controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const AWS = require('aws-sdk');
const nodemailer = require("nodemailer");
const axios = require("axios");

module.exports = createCoreController('api::patient.patient',
    ({ strapi }) => ({
        async findByPhoneNumber(ctx) {
            const { phone } = ctx.params;
            const patient = await strapi
                .query("api::patient.patient")
                .findOne({ where: { phone }, populate: true });
            return patient;
        },

        async checkUserExistByPhone(ctx) {
            const { phone } = ctx.params;
            let tPhone = phone;
            if (phone.startsWith('00')) tPhone = phone.replace('00', '0');
            const user = await strapi
                .query("plugin::users-permissions.user")
                .findOne({ where: { phone: tPhone }, populate: true });
            if (!!user) {
                return {
                    userExist: true,
                }
            } else {
                const patient = await strapi
                    .query("api::patient.patient")
                    .findOne({ where: { phone: tPhone }, populate: true });

                if (!!patient) {
                    return {
                        userExist: false,
                        patientExist: true,
                        patient,
                    }
                } else {
                    return {
                        userExist: false,
                        patientExist: false,
                        patient
                    }
                }
            }
        },

        async createUserFromPatient(ctx) {

            const patientId = ctx.request.body.patientId;
            const patient = await strapi
                .query("api::patient.patient")
                .findOne({ where: { id: patientId }, populate: true });

            let username = patient.phone;
            let email = ctx.request.body.email;

            if (!username) username = patient.phone;
            if (!email) email = patient.email;
            if (!email) email = "<EMAIL>";

            let user = await strapi.plugins['users-permissions'].services.user.add({
                blocked: false,
                confirmed: true,
                username,
                email,
                phone: patient.phone,
                password: ctx.request.body.password, //will be hashed automatically
                provider: 'local', //provider
                created_by: 1, //user admin id
                updated_by: 1, //user admin id
                role: 1, //role id
                patient: patientId
            });

            if (!patient.email && email) {
                await strapi.query("api::patient.patient")
                    .update({
                        where: { id: patientId },
                        data: { email: email }
                    });
            }

            return user;
        },

        async setFullNameI(ctx) {
            let patients = await strapi.query("api::patient.patient").findMany({});

            patients.forEach(p => {
                strapi
                    .query('api::patient.patient')
                    .update({
                        where: { id: p.id },
                        data: {
                            full_name_i: removeVietnameseTones(p.full_name ?? "")
                        }
                    });
            })

            return [];
        },

        async updatePatient(ctx) {
            const { field, value } = ctx.request.body;
            const { id } = ctx.state.user;
            const user = await strapi
                .query("plugin::users-permissions.user")
                .findOne({ where: { id }, populate: true });

            await strapi
                .query('api::patient.patient')
                .update({
                    where: { id: user.patient.id },
                    data: {
                        [field]: value,
                    }
                });

            return { ok: true };
        },

        async getRelationship(ctx) {
            const { id } = ctx.params;
            const user = await strapi
                .query("api::patient.patient")
                .findOne({
                    where: { id }, populate: {
                        relationships: {
                            populate: { patient: true }
                        }
                    }
                });


            return user;
        },

        async updatePatientRelationship(ctx) {
            const { relationship } = ctx.request.body;
            const { id } = ctx.params;

            console.log('relationship', relationship)

            await strapi.query("api::patient.patient")
                .update({
                    where: { id },
                    data: { relationships: relationship }
                });

            return { ok: true };
        },

        async createPatientAndUser(ctx) {
            const {
                name,
                email,
                password,
                phone,
                isWeb,
            } = ctx.request.body;

            let tPhone = phone;
            if (phone.startsWith('+840')) {
                tPhone = phone.replace('+840', '0');
            } else if (phone.startsWith('+84')) {
                tPhone = phone.replace('+84', '0');
            }

            const user = await strapi
                .query("plugin::users-permissions.user")
                .findOne({ where: { username: tPhone }, populate: true });
            if (!!user) {
                return {
                    userExist: true,
                }
            }

            let res = await strapi.plugins['users-permissions'].services.user.add({
                blocked: false,
                confirmed: true,
                username: tPhone,
                email: email,
                phone: tPhone,
                password: password, //will be hashed automatically
                provider: 'local', //provider
                created_by: 1, //user admin id
                updated_by: 1, //user admin id
                role: 1, //role id
            });

            const patient = await strapi
                .query("api::patient.patient")
                .create({
                    data: {
                        full_name: name,
                        email,
                        user: res.id,
                        password,
                        phone: tPhone,
                        patient_source: isWeb ? 2 : 1,
                        publishedAt: new Date(),
                    }
                });

            return res;
        },

        async createOTP(ctx) {
            const {
                name,
                email,
                password,
                phone,
                isWeb,
                role,
                birthday,
                gender,
                job,
                address,
                relative_phone,
                membership,
                internal,
                start_date_membership,
                membership_profile_file,
                source,
                discount,
                currentHealth,
                remaining_services,
                rem_services,
                address_cccd,
                cccd,
                relationships,
                ...otherFields
            } = ctx.request.body;

            if (!name) {
                return ctx.badRequest('Name are required');
            }

            let tPhone = phone;
            if (phone.startsWith('+840')) {
                tPhone = phone.replace('+840', '0');
            } else if (phone.startsWith('+84')) {
                tPhone = phone.replace('+84', '0');
            }

            const finalPassword = password || tPhone;

            const existingUser = await strapi
                .query("plugin::users-permissions.user")
                .findOne({ where: { username: tPhone }, populate: true });

            if (existingUser) {
                return {
                    status: 1,
                    message: 'User already exists',
                    userExist: true,
                };
            }

            try {
                const newUser = await strapi.plugins['users-permissions'].services.user.add({
                    blocked: false,
                    confirmed: true,
                    username: tPhone,
                    email: email || `${tPhone}@echomedi.com`,
                    phone: tPhone,
                    password: finalPassword,
                    provider: 'local',
                    created_by: 1,
                    updated_by: 1,
                    role: role || 1,
                });

                const patientData = {
                    full_name: name,
                    email: email || `${tPhone}@echomedi.com`,
                    user: newUser.id,
                    phone: tPhone,
                    patient_source: isWeb ? 2 : 1,
                    publishedAt: new Date(),
                };

                if (birthday) patientData.birthday = birthday;
                if (gender) patientData.gender = gender;
                if (job) patientData.job = job;
                if (address) patientData.address = address;
                if (relative_phone) patientData.relative_phone = relative_phone;
                if (membership) patientData.membership = membership;
                if (internal) patientData.internal = internal;
                if (start_date_membership) patientData.start_date_membership = start_date_membership;
                if (membership_profile_file) patientData.membership_profile_file = membership_profile_file;
                if (source) patientData.source = source;
                if (discount) patientData.discount = discount;
                if (currentHealth) patientData.currentHealth = currentHealth;
                if (remaining_services) patientData.remaining_services = remaining_services;
                if (rem_services) patientData.rem_services = rem_services;
                if (address_cccd) patientData.address_cccd = address_cccd;
                if (cccd) patientData.cccd = cccd;
                if (relationships) patientData.relationships = relationships;

                Object.keys(otherFields).forEach(key => {
                    if (otherFields[key] !== undefined && otherFields[key] !== null) {
                        patientData[key] = otherFields[key];
                    }
                });

                const patient = await strapi
                    .query("api::patient.patient")
                    .create({
                        data: patientData
                    });

                await sendAccountInfo(email || `${tPhone}@echomedi.com`, tPhone, name, tPhone, finalPassword);

                return {
                    status: 0,
                    message: 'Patient and user created successfully',
                    user: newUser,
                    patient: patient,
                };

            } catch (error) {
                console.error('Error creating patient and user:', error);
                return ctx.internalServerError('Failed to create patient and user: ' + error.message);
            }
        },
    }));


function removeVietnameseTones(str) {
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a")
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e")
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i")
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o")
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u")
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y")
    str = str.replace(/đ/g, "d")
    str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A")
    str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E")
    str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I")
    str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O")
    str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U")
    str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y")
    str = str.replace(/Đ/g, "D")
    // Some system encode vietnamese combining accent as individual utf-8 characters
    // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
    str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, "") // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
    str = str.replace(/\u02C6|\u0306|\u031B/g, "") // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
    // Remove extra spaces
    // Bỏ các khoảng trắng liền nhau
    str = str.replace(/ + /g, " ")
    str = str.trim()
    // Remove punctuations
    // Bỏ dấu câu, kí tự đặc biệt
    str = str.replace(
        /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,
        " "
    )
    return str
}

const sendAccountInfo = async (email, phone, name, username, password) => {
    try {
        if (email) {
            await sendAccountEmail(email, name, username, password);
        } else {
            await sendAccountSMS(phone, username, password);
        }
    } catch (error) {
        console.error('Error sending account info:', error);
    }
};

const sendAccountEmail = async (email, name, username, password) => {
    try {
        AWS.config.update({
            accessKeyId: process.env.AWS_SES_KEY,
            secretAccessKey: process.env.AWS_SES_SECRET,
            region: "us-east-1",
        });

        let transporter = nodemailer.createTransport({
            SES: new AWS.SES({
                apiVersion: '2010-12-01'
            })
        });

        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; color: #000000;">
                <h2 style="color: #000000;">Chào mừng <strong>${name}</strong>!</h2>

                <p>Tài khoản ECHO MEDI của quý khách đã được tạo thành công. Dưới đây là thông tin đăng nhập:</p>

                <div>
                    <p><strong>Tên đăng nhập:</strong> ${username}</p>
                    <p><strong>Mật khẩu:</strong> ${password}</p>
                </div>

                <p><strong>Lưu ý:</strong> Vui lòng đăng nhập và đổi mật khẩu để đảm bảo an toàn.</p>

                <p>Cảm ơn!
                <br><strong>ECHO MEDI</strong>
                <br />Hệ thống y tế toàn diện cho bạn và gia đình
                </p>
            </div>
        `;

        await transporter.sendMail({
            from: '<EMAIL>',
            to: email,
            subject: 'Thông tin tài khoản ECHO MEDI',
            html: html,
        });

        console.log('Account info email sent successfully to:', email);
    } catch (error) {
        console.error('Error sending account email:', error);
        throw error;
    }
};

const sendAccountSMS = async (phone, username, password) => {
    try {
        const token = await getToken();
        const message = `Tai khoan ECHO MEDI cua quy khach: Ten dang nhap: ${username}. Mat khau: ${password}. Vui long dang nhap va doi mat khau de dam bao an toan. Cam on!`;

        await sendSMS(phone, token, message);
        console.log('Account info SMS sent successfully to:', phone);
    } catch (error) {
        console.error('Error sending account SMS:', error);
        throw error;
    }
};

// Hàm lấy token cho SMS API
const getToken = async () => {
    try {
        const response = await axios.post('https://api01.sms.fpt.net/oauth2/token', {
            "client_id": "C011795D20124C70e521f1694861f91d3d54ce8e",
            "client_secret": "2d64A7c7c732fa39d9b43a3174926efab245679d7138dc2de56cb445d3454f9d6Cd4e0ba",
            "scope": "send_brandname_otp send_brandname",
            "session_id": "5c22be0c0396440829c98d7ba1240920",
            "grant_type": "client_credentials"
        });
        return response.data.access_token;
    } catch (error) {
        console.error('Error getting SMS token:', error);
        throw new Error('Error getting SMS token.');
    }
};

// Hàm gửi SMS
const sendSMS = async (phone, token, message) => {
    if (phone.startsWith('00')) phone = phone.replace('00', '0');

    try {
        await axios.post('http://api01.sms.fpt.net/api/push-brandname-otp', {
            "access_token": token,
            "session_id": "5c22be0c0396440829c98d7ba1240920",
            "BrandName": "ECHO MEDI",
            "Phone": phone,
            "Message": Buffer.from(message).toString('base64'),
            "RequestId": "tranID-Core01-" + Date.now()
        });
    } catch (error) {
        console.error('Error sending SMS:', error);
        throw new Error('Error sending SMS.');
    }
};