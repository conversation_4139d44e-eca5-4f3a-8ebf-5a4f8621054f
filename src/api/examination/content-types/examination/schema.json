{"kind": "collectionType", "collectionName": "examinations", "info": {"singularName": "examination", "pluralName": "examinations", "displayName": "Examination", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "en_title": {"type": "string"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "desc": {"type": "richtext"}, "en_desc": {"type": "richtext"}, "bundle_exam": {"type": "relation", "relation": "manyToOne", "target": "api::bundle-exam.bundle-exam", "inversedBy": "examinations"}, "slug": {"type": "uid", "targetField": "title"}}}