'use strict';

/**
 * examination controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::examination.examination',
    ({ strapi }) => ({
        async findOne(ctx) {
            const { slug } = ctx.params;
            var pkg = await strapi.db.query('api::examination.examination').findOne({
                populate: {
                    image: true,
                },
                where: {
                    slug
                }
            });

            return {
                package: pkg
            };
        },
    })
);
