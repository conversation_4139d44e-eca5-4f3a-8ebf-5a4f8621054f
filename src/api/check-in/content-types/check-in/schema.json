{"kind": "collectionType", "collectionName": "check_ins", "info": {"singularName": "check-in", "pluralName": "check-ins", "displayName": "CheckIn", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"checkedoutAt": {"type": "datetime"}, "metadata": {"type": "json"}, "personID": {"type": "string"}, "status": {"type": "enumeration", "enum": ["waiting", "progress", "done", "paid", "confirmed"], "default": "waiting"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "check_ins"}}}