'use strict';

/**
 * service controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::service.service',
    ({ strapi }) => ({
        async findOne(ctx) {
            const { slug } = ctx.params;
            var svc = await strapi.db.query('api::service.service').findOne({
                populate: {
                    image: true,
                    genetica_image: true,
                    sub_package: {
                        populate: {
                            package: true,
                            services: true,
                        }
                    },
                },
                where: {
                    slug
                }
            });

            return {
                service: svc
            };
        },

        async updateServicesJson(ctx) {
            // const services = await strapi.db.query('api::service.service').findMany()

            const services = await strapi.db.query('api::service.service').findMany({
                offset: 0, 
                limit: -1,
            });
            
            console.log('services', services);  
        },

        async updateBundledServicesJson(ctx) {

        }
    }));
