{"kind": "collectionType", "collectionName": "services", "info": {"singularName": "service", "pluralName": "services", "displayName": "Service", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"label": {"type": "text", "pluginOptions": {}}, "slug": {"type": "uid", "targetField": "label"}, "desc": {"type": "text", "pluginOptions": {}}, "price": {"type": "biginteger", "pluginOptions": {}}, "detail": {"type": "richtext", "pluginOptions": {}}, "sub_package": {"type": "relation", "relation": "manyToOne", "target": "api::sub-package.sub-package", "inversedBy": "services"}, "show_price": {"pluginOptions": {}, "type": "boolean"}, "show_buy_btn": {"pluginOptions": {}, "type": "boolean"}, "show_learn_more": {"pluginOptions": {}, "type": "boolean"}, "en_label": {"pluginOptions": {}, "type": "text"}, "en_desc": {"pluginOptions": {}, "type": "string"}, "en_detail": {"pluginOptions": {}, "type": "richtext"}, "show_additional_fee": {"type": "boolean"}, "show_inquiry_form": {"type": "boolean"}, "show_booking_btn": {"type": "boolean"}, "detail_mobile": {"type": "richtext"}, "en_detail_mobile": {"type": "richtext"}, "benefit": {"type": "string"}, "properties": {"type": "json"}, "genetica_image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "genetica_pdf": {"type": "string"}, "en_benefit": {"type": "string"}, "en_properties": {"type": "json"}, "specification": {"type": "json"}, "en_specification": {"type": "json"}, "type": {"type": "enumeration", "enum": ["other", "gen_detail", "membership", "policy"]}, "label_web": {"type": "text"}, "label_web_en": {"type": "text"}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "original_price": {"type": "biginteger"}}}