{"kind": "collectionType", "collectionName": "events", "info": {"singularName": "event", "pluralName": "events", "displayName": "Event", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"fullName": {"type": "string"}, "phoneNumber": {"type": "string"}, "knownEchoMedi": {"type": "enumeration", "enum": ["Đã biết", "<PERSON><PERSON><PERSON>"]}, "knownFamilyDoctor": {"type": "enumeration", "enum": ["Đã biết", "<PERSON><PERSON><PERSON>"]}, "healthConcern": {"type": "json"}, "gamePlayed": {"type": "enumeration", "enum": ["<PERSON><PERSON> r<PERSON>a tay", "Bắn bóng đổi quà", "EVENTS"]}, "score": {"type": "string"}, "eventName": {"type": "string"}, "eventCode": {"type": "enumeration", "enum": ["HUNGVUONG2025", "PMH2025", "EVENTS"]}, "giftSource": {"type": "json"}, "selected_gifts": {"type": "relation", "relation": "manyToMany", "target": "api::gift.gift", "inversedBy": "events"}}}