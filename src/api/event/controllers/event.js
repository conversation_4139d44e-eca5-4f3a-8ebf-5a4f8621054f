'use strict';

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::event.event', ({ strapi }) => ({

  async find(ctx) {
    try {
      const events = await strapi.entityService.findMany('api::event.event', {
        sort: { createdAt: 'desc' },
        populate: ['selected_gifts'],
      });

      return ctx.send({ success: true, data: events }, 200);
    } catch (err) {
      return ctx.send({ success: false, error: err.message }, 500);
    }
  },

  async create(ctx) {
    try {
      const data = ctx.request.body;
      const { phoneNumber } = data;

      const existingUsers = await strapi.entityService.findMany('api::event.event', {
        filters: { phoneNumber },
        limit: 1,
      });

      if (existingUsers.length > 0) {
        return ctx.send({ success: false, message: 'Phone number already exists' }, 400);
      }

      const createdEvent = await strapi.entityService.create('api::event.event', {
        data,
      });

      return ctx.send({ success: true, data: createdEvent }, 201);

    } catch (err) {
      return ctx.send({ success: false, error: err.message }, 500);
    }
  },
  async updateGameInfo(ctx) {
    try {
      const { phoneNumber, gamePlayed, score, selectedGift, giftSource } = ctx.request.body;

      if (!phoneNumber || !selectedGift) {
        return ctx.send({ success: false, message: 'Phone number and selected gift are required' }, 400);
      }

      const events = await strapi.entityService.findMany('api::event.event', {
        filters: { phoneNumber },
        populate: ['selected_gifts'],
        sort: [{ createdAt: 'desc' }],
        limit: 1,
      });

      if (events.length === 0) {
        return ctx.send({ success: false, message: 'Event not found for this phone number' }, 404);
      }

      const event = events[0];

      if (event.selected_gifts.length >= 2) {
        return ctx.send({ success: false, message: 'User has already received 2 gifts' }, 400);
      }

      const alreadySelected = event.selected_gifts.find(gift => gift.id === selectedGift);
      if (alreadySelected) {
        return ctx.send({ success: false, message: 'This gift has already been selected' }, 400);
      }

      const gift = await strapi.entityService.findOne('api::gift.gift', selectedGift, {
        fields: ['id', 'currentCount', 'maxCount'],
      });

      if (!gift) {
        return ctx.send({ success: false, message: 'Gift not found' }, 404);
      }

      if (gift.currentCount >= gift.maxCount) {
        return ctx.send({ success: false, message: 'Gift is out of stock' }, 400);
      }

      await strapi.entityService.update('api::gift.gift', selectedGift, {
        data: {
          currentCount: gift.currentCount + 1,
        },
      });
      const currentGiftSources = Array.isArray(event.giftSource) ? event.giftSource : [];

      if (giftSource) {
        if (Array.isArray(giftSource)) {
          currentGiftSources.push(...giftSource); 
        } else {
          currentGiftSources.push(giftSource); 
        }
      }


      const updatedEvent = await strapi.entityService.update('api::event.event', event.id, {
        data: {
          gamePlayed,
          score,
          selected_gifts: {
            connect: [{ id: selectedGift }],
          },
          giftSource: currentGiftSources,
        },
        populate: ['selected_gifts'],
      });

      return ctx.send({ success: true, data: updatedEvent }, 200);
    } catch (err) {
      return ctx.send({ success: false, error: err.message }, 500);
    }
  },
  async findByEventCode(ctx) {
    try {
      const { code } = ctx.params;

      const events = await strapi.entityService.findMany('api::event.event', {
        filters: { eventCode: code },
        sort: { createdAt: 'desc' },
        populate: ['selectedGift'],
      });

      return ctx.send({ success: true, data: events }, 200);
    } catch (err) {
      return ctx.send({ success: false, error: err.message }, 500);
    }
  },

  async createEvent(ctx) {
    try {
      const {
        fullName,
        phoneNumber,
        giftSource,
        selectedGift,
        selected_gifts
      } = ctx.request.body;

      if (!fullName || !phoneNumber) {
        return ctx.send({
          success: false,
          message: 'Full name and phone number are required'
        }, 400);
      }

      const phoneRegex = /^(0[3|5|7|8|9])+([0-9]{8})$/;
      if (!phoneRegex.test(phoneNumber)) {
        return ctx.send({
          success: false,
          message: 'Invalid phone number format'
        }, 400);
      }

      const nameRegex = /^[A-Za-zÀ-ỹ\s]{2,}$/;
      if (!nameRegex.test(fullName)) {
        return ctx.send({
          success: false,
          message: 'Invalid name format'
        }, 400);
      }

      const existingEvent = await strapi.entityService.findMany('api::event.event', {
        filters: { phoneNumber },
        limit: 1,
      });

      if (existingEvent.length > 0) {
        return ctx.send({
          success: false,
          message: 'Phone number already exists'
        }, 400);
      }

      const giftIds = [];

      if (selectedGift) {
        giftIds.push(selectedGift);
      } else if (Array.isArray(selected_gifts)) {
        giftIds.push(...selected_gifts);
      }

      for (const giftId of giftIds) {
        const gift = await strapi.entityService.findOne('api::gift.gift', giftId, {
          fields: ['id', 'currentCount', 'maxCount'],
        });

        if (!gift) {
          return ctx.send({ success: false, message: `Gift ID ${giftId} not found` }, 404);
        }

        if (gift.currentCount >= gift.maxCount) {
          return ctx.send({ success: false, message: `Gift ID ${giftId} is out of stock` }, 400);
        }

        await strapi.entityService.update('api::gift.gift', giftId, {
          data: { currentCount: gift.currentCount + 1 },
        });
      }

      const eventData = {
        fullName,
        phoneNumber,
        knownEchoMedi: "Chưa biết",
        knownFamilyDoctor: "Chưa biết",
        healthConcern: [],
        gamePlayed: "EVENTS",
        score: "100",
        eventName: 'EVENTS',
        eventCode: 'EVENTS',
        giftSource,
      };

      if (giftIds.length > 0) {
        eventData.selected_gifts = {
          connect: giftIds.map(id => ({ id })),
        };
      }

      const createdEvent = await strapi.entityService.create('api::event.event', {
        data: eventData,
        populate: ['selected_gifts'],
      });

      return ctx.send({ success: true, data: createdEvent }, 201);

    } catch (err) {
      return ctx.send({ success: false, error: err.message }, 500);
    }
  },

}));
