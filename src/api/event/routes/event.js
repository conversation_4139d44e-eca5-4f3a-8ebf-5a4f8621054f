'use strict';

module.exports = {
  routes: [
    {
      method: 'POST',
      path: '/events',
      handler: 'event.create', 
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/events/update-game',
      handler: 'event.updateGameInfo',
      config: {
        policies: [],
        middlewares: [],
      },
    },

    {
      method: 'GET',
      path: '/events',
      handler: 'event.find',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/events/:id',
      handler: 'event.findOne', 
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/events/by-code/:code',
      handler: 'event.findByEventCode', 
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/events/create-event',
      handler: 'event.createEvent',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
