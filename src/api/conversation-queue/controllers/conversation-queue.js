'use strict';
const axios = require("axios");

/**
 * conversation-queue controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
var admin = require("firebase-admin");
const client = require('twilio')(process.env.ACCOUNT_SID, process.env.AUTH_TOKEN)

module.exports = createCoreController('api::conversation-queue.conversation-queue',
    ({ strapi }) => ({
        async twilioBalance(ctx) {
            // return 1;
            return await client.balance.fetch()
                .then((data) => {
                    const balance = Math.round(data.balance * 100) / 100;
                    const currency = data.currency;
                    return `Twilio ${balance} ${currency}.`;
                });
        },
        async makeCall(ctx) {
            const { roomId, user } = ctx.request.body;
            const { id } = ctx.state.user;


            const caller = await strapi.query('plugin::users-permissions.user').findOne({
                where: {
                    id,
                },
                populate: {
                    patient: true,
                }
            });

            const usr = await strapi.query('plugin::users-permissions.user').findOne({
                where: {
                    id: user
                },
            });

            const msg = {
                "token": usr.token,
                "notification": {
                    "title": "ECHO MEDI",
                    "body": "ECHO MEDI đang gọi cho bạn",
                },
                "data": {
                    roomId: roomId,
                    otherId: id.toString(),
                    otherName: caller?.patient?.full_name ?? "",
                },
                apns: {
                    payload: {
                        aps: {
                            'mutable-content': 1,
                            'content-available': 1
                        }
                    }
                },
                "webpush": {
                    "headers": {
                        "Urgency": "high"
                    },
                    "fcm_options": {
                        "link": "google.com"
                    }
                },
                "android": {
                    "priority": "high"
                }
            };

            admin.messaging().send(msg)
                .then((response) => {
                    console.log('response send fcm msg', response)
                })
                .catch((error) => {
                    console.log('error when send fcm msg', error)
                });



            return { ok: true };
        },

        async supportClient(ctx) {
            const { id } = ctx.params;
            let cq = await strapi
                .query('api::conversation-queue.conversation-queue')
                .findOne({
                    where: { id },
                    populate: {
                        second_person: {
                            populate: true,
                        }
                    }
                });

            if (cq.second_person) {
                return {
                    ok: false,
                }
            }

            let res = await strapi
                .query('api::conversation-queue.conversation-queue')
                .update({
                    where: { id },
                    data: { second_person: ctx.request.body.second_person }
                });

            return { ok: true }
        },

        async create(ctx) {
            const { id } = ctx.state.user;
            const user = await strapi
                .query("plugin::users-permissions.user")
                .findOne({ where: { id }, populate: true });
            const { locale } = ctx.request.body;
            var msg = locale == "vi"
                ? "0|room1|Xin chào! Chúng tôi có thể giúp bạn thế nào ? Trường hợp khẩn cấp, vui lòng liên hệ hotline 1900 638 408 hoặc với cơ sở y tế gần nhất"
                : "0|room1|Hi! How can we help you? For emergencies, please contact hotline 1900 638 408 or your local authorities.";

            if (ctx.request.body.branch == "all") {
                ctx.request.body.note = "Khám từ xa";
            }

            const conversation = await strapi.query("api::conversation-queue.conversation-queue").create({
                data: {
                    ...ctx.request.body,
                    'data': [msg],
                    'latest_message': msg,
                    latest: new Date(),
                    publishedAt: new Date(),
                }
            });
            try {
                const msg2 = {
                    "topic": "doctor",
                    "notification": {
                        "title": `Yêu cầu hội thoại ${ctx.request.body.supporter == "doctor" ? "Bác sĩ" : "CC"} mới - ${ctx.request.body.branch}`,
                        "body": user?.patient?.full_name,
                    },
                    data: {
                        "topic": "doctor",
                    },
                    "webpush": {
                        "fcm_options": {
                            "link": "google.com"
                        }
                    }
                };

                admin.messaging().send(msg2)
                    .then((response) => {
                        console.log('sending msg to topic', response)
                    })
                    .catch((error) => {
                        console.log('error when send fcm msg', error)
                    });

                let admins = await strapi.query("plugin::users-permissions.user").findMany({
                    where: {
                        $or: [
                            {
                                role: {
                                    type: conversation.supporter == "care-concierge" ? "care_concierge" : "doctor"
                                }
                            }
                        ]
                    },
                });

                let tokens = admins.map(a => a.fcmToken);
                let uniqTokens = [...new Set(tokens)];
                uniqTokens.forEach(t => {
                    if (!!t) {
                        const msg = {
                            "token": t,
                            "notification": {
                                "title": `HIS: Yêu cầu hội thoại ${ctx.request.body.supporter == "doctor" ? "Bác sĩ" : "CC"} mới - ${ctx.request.body.branch}`,
                                "body": user?.patient?.full_name,
                            },
                            "webpush": {
                                "fcm_options": {
                                    "link": "google.com"
                                }
                            }
                        };
                        admin.messaging().send(msg)
                            .then((response) => {
                            })
                            .catch((error) => {
                                console.log('error when send fcm msg', error)
                            });
                    }
                })


            } catch (e) {
                console.log('exception', e)
            }
            return conversation;
        },

        async getMessages(ctx) {
            const { id } = ctx.params;
            const { id: userId } = ctx.state.user;

            let chat = await strapi
                .query('api::conversation-queue.conversation-queue')
                .findOne({
                    where: { id },
                    populate: {
                        conversation: true,
                        user: {
                            populate: {
                                patient: true,
                            }
                        },
                        second_person: {
                            populate: {
                                patient: true
                            }
                        },
                    }
                });

            if (chat.user.id == userId && !chat.user_seen) {
                if (!chat.user_seen) {
                    await strapi
                        .query('api::conversation-queue.conversation-queue')
                        .update({
                            where: { id },
                            data: { user_seen: true, user_seen_date: Date.now(), }
                        });
                }
                const [_, userCount] = await strapi.db.query('api::conversation-queue.conversation-queue').findWithCount({
                    where: { user: userId, user_seen: false },
                });
                chat.count = userCount;
            } else {
                if (!chat.second_person_seen) {
                    await strapi
                        .query('api::conversation-queue.conversation-queue')
                        .update({
                            where: { id },
                            data: { second_person_seen: true, second_person_seen_date: Date.now(), }
                        });
                }
                const [_, userCount] = await strapi.db.query('api::conversation-queue.conversation-queue').findWithCount({
                    where: { second_person: userId, second_person_seen: false },
                });
                chat.count = userCount;
            }

            return chat;
        },

        async updateSecondPerson(ctx) {
            const { id, pid } = ctx.request.body;

            await strapi
                .query('api::conversation-queue.conversation-queue')
                .update({
                    where: { id },
                    data: { second_person: pid }
                });

            const res = await strapi
                .query('api::conversation-queue.conversation-queue')
                .findOne({
                    where: { id },
                    populate: {
                        user: {
                            populate: true
                        }
                    }
                });

            const secondUser = await strapi
                .query("plugin::users-permissions.user")
                .findOne({ where: { id: pid } });

            const msg = {
                "token": secondUser.token,
                "notification": {
                    "title": `Hội thoại được chuyển tiếp:`,
                    "body": res?.user?.patient?.full_name ?? '',
                },
                // "data": {
                //     roomId: msgs[1],
                //     notify: "true",
                //     otherName,
                //     otherId: msgs[0],
                // },
                apns: {
                    payload: {
                        aps: {
                            'mutable-content': 1,
                            'content-available': 1
                        }
                    }
                },
                "webpush": {
                    "fcm_options": {
                        "link": "google.com"
                    }
                }
            };
            admin.messaging().send(msg)
                .then((response) => {
                })
                .catch((error) => {
                    console.log('error when send fcm msg', error)
                });

            return res;
        },

        async updateStatus(ctx) {
            const { id, status } = ctx.request.body;

            const res = await strapi
                .query('api::conversation-queue.conversation-queue')
                .update({
                    where: { id },
                    data: { status }
                });

            return res;
        },

        async addMessage(ctx) {
            const { id } = ctx.request.body;
            let chat = await strapi
                .query('api::conversation-queue.conversation-queue')
                .findOne({
                    where: { id },
                    populate: {
                        conversation: true,
                        user: true,
                        second_person: true,
                    }
                });

            if (!chat) return;

            try {
                let data = [];
                if (chat?.data) {
                    data = chat?.data;
                    if (!Array.isArray(data)) {
                        data = [data];
                    }
                }
                var msgs = ctx.request.body.message.split("|");
                data.push(ctx.request.body.message);
                let payload = {
                    data,
                    user_seen: msgs[0] == chat.user?.id.toString(),
                    second_person_seen: msgs[0] == chat.second_person?.id.toString(),
                };

                if (!ctx.request.body.message?.includes('[UPDATE_SEEN]') && !ctx.request.body.message?.includes('[ASSIGNED]')) {
                    payload['latest_message'] = ctx.request.body.message;
                    payload['latest'] = new Date();
                }

                await strapi
                    .query('api::conversation-queue.conversation-queue')
                    .update({
                        where: { id },
                        data: payload
                    });
            } catch (e) {

            }
            const [_, userCount] = await strapi.db.query('api::conversation-queue.conversation-queue').findWithCount({
                where: { user: chat.user.id, user_seen: false },
            });
            let cq = await strapi
                .query('api::conversation-queue.conversation-queue')
                .findOne({
                    where: { id },
                    populate: {
                        user: {
                            populate: true,
                        },
                        second_person: {
                            populate: true,
                        }
                    }
                });
            let count = userCount;
            if (chat.second_person && msgs[0] == cq.user.id) {
                const [__, secondUserCount] = await strapi.db.query('api::conversation-queue.conversation-queue').findWithCount({
                    where: { second_person: chat.second_person.id, second_person_seen: false },
                });
                count = secondUserCount;
            }

            let token = msgs[0] == cq.user.id ? cq.second_person?.token : cq.user?.token;
            let otherName = msgs[0] != cq.user.id ? cq.second_person?.patient?.full_name : cq.user.patient?.full_name;
            try {
                if (token) {
                    const msg = {
                        "token": token,
                        "notification": {
                            "title": "Tin nhắn mới: " + otherName,
                            "body": msgs[2],
                        },
                        "data": {
                            type: "new_message",
                            click_action: "FLUTTER_NOTIFICATION_CLICK",
                            roomId: msgs[1],
                            notify: "true",
                            callerName: otherName,
                            count: count.toString(),
                            otherId: msgs[0],
                        },
                        apns: {
                            payload: {
                                aps: {
                                    'mutable-content': 1,
                                    'content-available': 1
                                }
                            }
                        },
                        "webpush": {
                            "fcm_options": {
                                "link": "google.com"
                            }
                        }
                    };
                    if (msgs[2] != "[UPDATE_SEEN]") {
                        admin.messaging().send(msg)
                            .then((response) => {
                            })
                            .catch((error) => {
                                console.log('error when send fcm msg', error)
                            });
                    }
                }
            } catch (e) {

            }

            return {};
        }
    }));
