{"kind": "collectionType", "collectionName": "conversation_queues", "info": {"singularName": "conversation-queue", "pluralName": "conversation-queues", "displayName": "Conversation Queue", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "data": {"type": "json"}, "conversation": {"type": "relation", "relation": "oneToOne", "target": "api::conversation.conversation", "inversedBy": "conversation_queue"}, "latest_message": {"type": "string"}, "second_person": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "waiting": {"type": "boolean"}, "supporter": {"type": "enumeration", "enum": ["doctor", "care-concierge"]}, "branch": {"type": "string"}, "user_seen": {"type": "boolean"}, "second_person_seen": {"type": "boolean"}, "latest": {"type": "datetime"}, "note": {"type": "string"}, "status": {"type": "enumeration", "enum": ["incomplete", "complete"]}, "user_seen_date": {"type": "datetime"}, "second_person_seen_date": {"type": "datetime"}}}