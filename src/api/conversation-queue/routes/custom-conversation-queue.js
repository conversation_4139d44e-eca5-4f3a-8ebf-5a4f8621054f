module.exports = {
    routes: [
      {
        method: "GET",
        path: "/conversation-queue/twilioBalance",
        handler: "conversation-queue.twilioBalance",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/conversation-queue/addMessage",
        handler: "conversation-queue.addMessage",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/conversation-queue/create",
        handler: "conversation-queue.create",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/conversation-queue/makeCall",
        handler: "conversation-queue.makeCall",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "GET",
        path: "/conversation-queue/getMessages/:id",
        handler: "conversation-queue.getMessages",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/conversation-queue/supportClient/:id",
        handler: "conversation-queue.supportClient",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/conversation-queue/updateSecondPerson",
        handler: "conversation-queue.updateSecondPerson",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        path: "/conversation-queue/updateStatus",
        handler: "conversation-queue.updateStatus",
        config: {
          policies: [],
          prefix: false,
        },
      },
    ]}