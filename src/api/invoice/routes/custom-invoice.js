module.exports = {
    routes: [
      {
        method: "PUT",
        handler: "invoice.updateAndDownloadInvoice",
        path: "/invoice/updateAndDownloadInvoice/:id",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        handler: "invoice.getRevenue",
        path: "/invoice/getRevenue",
        config: {
          policies: [],
          prefix: false,
        },
      },
      {
        method: "POST",
        handler: "invoice.downloadRevenueReport",
        path: "/invoice/downloadRevenueReport",
        config: {
          policies: [],
          prefix: false,
        },
        
      },
      {
        method: "POST",
        path: "/invoice/markInvoiceAsPaid",
        handler: "invoice.markInvoiceAsPaid",
        config: {
          policies: [],
          prefix: "",
        },
      },
    ]
}  