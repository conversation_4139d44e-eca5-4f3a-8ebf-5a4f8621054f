{"kind": "collectionType", "collectionName": "invoices", "info": {"singularName": "invoice", "pluralName": "invoices", "displayName": "invoice", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"uid": {"type": "uid"}, "booking": {"type": "relation", "relation": "oneToOne", "target": "api::booking.booking"}, "data": {"type": "json"}, "idu": {"type": "biginteger"}, "cashier_in_charge": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}