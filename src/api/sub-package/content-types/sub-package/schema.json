{"kind": "collectionType", "collectionName": "sub_packages", "info": {"singularName": "sub-package", "pluralName": "sub-packages", "displayName": "Sub Package", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"label": {"type": "text", "pluginOptions": {}}, "image": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"], "pluginOptions": {}}, "package": {"type": "relation", "relation": "manyToOne", "target": "api::package.package", "inversedBy": "sub_packages"}, "services": {"type": "relation", "relation": "oneToMany", "target": "api::service.service", "mappedBy": "sub_package"}, "en_label": {"pluginOptions": {}, "type": "text"}}}