'use strict';
const dayjs = require("dayjs");
const axios = require("axios");
var admin = require("firebase-admin");

var serviceAccount = require("./echomedi-551ad-firebase-adminsdk-xbqg4-59a3438e3e.json");

module.exports = {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register(/*{ strapi }*/) { },

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  bootstrap({ strapi }) {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });

    var io = require("socket.io")(strapi.server.httpServer, {
      cors: { // cors setup
        origin: "http://localhost:3000",
        methods: ["GET", "POST"],
        allowedHeaders: ["my-custom-header"],
        credentials: true,
      },
    });

    io.use((socket, next) => {
      const username = socket.handshake.auth.username;
      if (!username) {
        return next(new Error("invalid username"));
      }
      const roomId = socket.handshake.auth.roomId;
      console.log('roomId', socket.handshake.auth)
      socket.join(roomId);
      socket.username = username;
      next();
    });

    io.on("connection", function (socket) { //Listening for a connection from the frontend
      const users = [];
      for (let [id, socket] of io.of("/").sockets) {
        users.push({
          userID: id,
          username: socket.username,
        });
      }
      socket.emit("users", users);

      socket.on("editMessage", async (data) => {
        let cq = await strapi
          .query('api::conversation-queue.conversation-queue')
          .findOne({
            where: { id: data.id },
          });

        let msgs = cq.data[data.mid].split("|");
        msgs[2] = data.message;

        cq.data[data.mid] = msgs.join("|");

        await strapi
          .query('api::conversation-queue.conversation-queue')
          .update({
            where: { id: data.id },
            data: {
              data: cq.data,
            }
          });

          try {
            console.log('msg', data.mid + "[|]" + cq.data[data.mid])
            const responses = io.timeout(10000).to(msgs[1]).emit("editMessage", data.mid + "[|]" + cq.data[data.mid]);
            console.log(responses); // one response per client
          } catch (e) {
            console.log('error', e);
            // some clients did not acknowledge the event in the given delay
          }
      });

      socket.on("chatMessage", async (data) => { // Listening for a sendMessage connection
        var msgs = data.message.split("|");
        try {
          data.message = data.message.replace("Đã tham gia", "Đã tham gia.");
          const responses = io.timeout(10000).to(msgs[1]).emit("chatMessage", data.message);
          console.log(responses); // one response per client
        } catch (e) {
          console.log('error', e);
          // some clients did not acknowledge the event in the given delay
        }
        // io.to(msgs[1]).emitWithAck("chatMessage", [data.message, (a) => {
        //   console.log('data', a);
        // }]);
        // console.log('data', data);
        try {
          await axios({
            method: "POST",
            url: "http://localhost:1337/api/conversation-queue/addMessage",
            data: {
              id: msgs[1].slice(4),
              message: data.message,
            },
          });
        } catch (e) {
          console.log('Error when add message', e);
        }

        // io.emit("chatListMessage", data.message);
      });
    });

    strapi.db.lifecycles.subscribe({
      models: ["api::patient.patient"],
      async afterCreate(event) {
        const { params, result } = event;

        await strapi.query("api::patient.patient")
          .update({
            where: { id: result.id },
            data: {
              uid: getUserUID("P", result.id),
              full_name_i: removeVietnameseTones(result.full_name ?? "")
            }
          });
      },

      async afterUpdate(event) {
        const { params, result } = event;

        if (removeVietnameseTones(result.full_name) != result.full_name_i) {
          await strapi.query("api::patient.patient")
            .update({
              where: { id: result.id },
              data: { full_name_i: removeVietnameseTones(result.full_name) }
            });
        }
      }
    });

    strapi.db.lifecycles.subscribe({
      models: ["api::service-bundle.service-bundle"],
      async afterCreate(event) {
        const { params, result } = event;

        await strapi.query("api::service-bundle.service-bundle")
          .update({
            where: { id: result.id },
            data: { label_i: removeVietnameseTones(result.label) }
          });
      },

      async afterUpdate(event) {
        const { params, result } = event;

        if (removeVietnameseTones(result.label) != result.label_i) {
          await strapi.query("api::service-bundle.service-bundle")
            .update({
              where: { id: result.id },
              data: { label_i: removeVietnameseTones(result.label) }
            });
        }
      }
    });

    strapi.db.lifecycles.subscribe({
      models: ["api::medical-service.medical-service"],
      async afterCreate(event) {
        const { params, result } = event;

        await strapi.query("api::medical-service.medical-service")
          .update({
            where: { id: result.id },
            data: { label_i: removeVietnameseTones(result.label) }
          });
      },

      async afterUpdate(event) {
        const { params, result } = event;

        if (removeVietnameseTones(result.label) != result.label_i) {
          await strapi.query("api::medical-service.medical-service")
            .update({
              where: { id: result.id },
              data: { label_i: removeVietnameseTones(result.label) }
            });
        }
      }
    });

    strapi.db.lifecycles.subscribe({
      models: ["api::prescription.prescription"],
      async afterCreate(event) {
        const { params, result } = event;

        await strapi.query("api::prescription.prescription")
          .update({
            where: { id: result.id },
            data: { uid: getUserUID("PR", result.id) }
          });
      },
    });

    strapi.db.lifecycles.subscribe({
      models: ["api::invoice.invoice"],
      async afterCreate(event) {
        const { params, result } = event;

        const prevInv = await strapi.query("api::invoice.invoice")
          .findOne({
            where: { id: result.id - 1 },
          });

        if (prevInv)
        await strapi.query("api::invoice.invoice")
          .update({
            where: { id: result.id },
            data: {
              idu: dayjs().format("YYMM") + (dayjs(prevInv.createdAt).month() == dayjs(result.createdAt).month()
                ? pad(parseInt(prevInv.idu.toString().substring(4, 7)) + 1, 3) : "001")
            }
          });
      },
    });

    strapi.db.lifecycles.subscribe({
      models: ["api::medical-record.medical-record"],
      async afterCreate(event) {
        const { params, result } = event;

        await strapi.query("api::medical-record.medical-record")
          .update({
            where: { id: result.id },
            data: { uid: getUserUID("M", result.id) }
          });
      },
    });
  },
};

const getUserUID = (prefix, id) => {
  return prefix + pad(id, 6);
}

function pad(num, size) {
  var s = "000000000" + num;
  return s.substr(s.length - size);
}

function removeVietnameseTones(str) {
  if (!str) return str;
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, "a")
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, "e")
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, "i")
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, "o")
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, "u")
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, "y")
  str = str.replace(/đ/g, "d")
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, "A")
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, "E")
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, "I")
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, "O")
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, "U")
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, "Y")
  str = str.replace(/Đ/g, "D")
  // Some system encode vietnamese combining accent as individual utf-8 characters
  // Một vài bộ encode coi các dấu mũ, dấu chữ như một kí tự riêng biệt nên thêm hai dòng này
  str = str.replace(/\u0300|\u0301|\u0303|\u0309|\u0323/g, "") // ̀ ́ ̃ ̉ ̣  huyền, sắc, ngã, hỏi, nặng
  str = str.replace(/\u02C6|\u0306|\u031B/g, "") // ˆ ̆ ̛  Â, Ê, Ă, Ơ, Ư
  // Remove extra spaces
  // Bỏ các khoảng trắng liền nhau
  str = str.replace(/ + /g, " ")
  str = str.trim()
  // Remove punctuations
  // Bỏ dấu câu, kí tự đặc biệt
  str = str.replace(
    /!|@|%|\^|\*|\(|\)|\+|\=|\<|\>|\?|\/|,|\.|\:|\;|\'|\"|\&|\#|\[|\]|~|\$|_|`|-|{|}|\||\\/g,
    " "
  )
  return str
}
