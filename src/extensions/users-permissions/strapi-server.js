const { sanitize } = require("@strapi/utils");
const { ApplicationError, ValidationError } = require("@strapi/utils").errors;
const dayjs = require("dayjs");
const qs = require("qs");
const _ = require("lodash");
const { getService } = require('../utils');
const {
  validateCallbackBody,
  validateRegisterBody,
  validateSendEmailConfirmationBody,
  validateForgotPasswordBody,
  validateResetPasswordBody,
  validateEmailConfirmationBody,
  validateChangePasswordBody,
} = require('./validation/auth');
var crypto = require("crypto");
const AWS = require('aws-sdk');
const nodemailer = require("nodemailer");
const axios = require("axios");

const emailRegExp =
  /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

const sanitizeUser = (user, ctx) => {
  const { auth } = ctx.state;
  const userSchema = strapi.getModel("plugin::users-permissions.user");

  return sanitize.contentAPI.output(user, userSchema, { auth });
};

module.exports = (plugin) => {

  plugin.controllers.user.callback = async (ctx) => {
    const provider = ctx.params.provider || 'local';
    const params = ctx.request.body;

    const store = strapi.store({ type: 'plugin', name: 'users-permissions' });
    const grantSettings = await store.get({ key: 'grant' });

    const grantProvider = provider === 'local' ? 'email' : provider;

    if (!_.get(grantSettings, [grantProvider, 'enabled'])) {
      throw new ApplicationError('This provider is disabled');
    }

    if (provider === 'local') {
      await validateCallbackBody(params);

      const { identifier } = params;

      let ors = [
        { email: identifier.toLowerCase() },
        { username: identifier },
        { phone: identifier }
      ];

      if (identifier[0] == '0') {
        pn = '+84' + identifier.substring(1);
        ors.push({ phone: '+84' + identifier.substring(1) });
      }
      if (identifier[0] != '+') {
        pn2 = '+' + identifier;
        ors.push({ phone: '+' + identifier });
      }

      // Check if the user exists.
      const user = await strapi.query('plugin::users-permissions.user').findOne({
        where: {
          provider,
          $or: ors,
        },
      });

      if (!user) {
        throw new ValidationError('Invalid identifier or password');
      }

      if (!user.password) {
        throw new ValidationError('Invalid identifier or password');
      }

      const validPassword = await getService('user').validatePassword(
        params.password,
        user.password
      );

      if (!validPassword) {
        throw new ValidationError('Invalid identifier or password');
      }

      const advancedSettings = await store.get({ key: 'advanced' });
      const requiresConfirmation = _.get(advancedSettings, 'email_confirmation');

      if (requiresConfirmation && user.confirmed !== true) {
        throw new ApplicationError('Your account email is not confirmed');
      }

      if (user.blocked === true) {
        throw new ApplicationError('Your account has been blocked by an administrator');
      }

      // const token = params.token;
      // await strapi.query("plugin::users-permissions.user")
      //   .update({
      //     where: { id: user.id },
      //     data: { token: token, last_logged_in: Date.now() }
      //   });

      return ctx.send({
        status: 0,
        jwt: getService('jwt').issue({ id: user.id }),
        userUUID: '9ae1809e-a171-4816-b13d-a6a9f2c5c2fd',
        user: await sanitizeUser(user, ctx),
      });
    }

    // Connect the user with the third-party provider.
    try {
      const user = await getService('providers').connect(provider, ctx.query);

      return ctx.send({
        status: 0,
        jwt: getService('jwt').issue({ id: user.id }),
        user: await sanitizeUser(user, ctx),
      });
    } catch (error) {
      throw new ApplicationError(error.message);
    }
  },

  plugin.controllers.user.login = async (ctx) => {
    const { id } = ctx.state.user;
    // Check if the user exists.
    const user = await strapi.query('plugin::users-permissions.user').findOne({
      where: {
        id,
      },
    });
    
    return ctx.send({
      status: 0,
      jwt: getService('jwt').issue({ id: user.id }),
      userUUID: 'f1b28bfb-8631-4783-ad8f-f4232abb6038',
      user: await sanitizeUser(user, ctx),
    });
  },

  plugin.controllers.auth.verifyOTP = async (ctx) => {
    // const { email, otp } = ctx.request.body;

    // const user = await strapi
    //   .query('plugin::users-permissions.user')
    //   .findOne({ where: { email: email.toLowerCase() } });

    // if (user.resetPasswordToken == otp) {
    //   return { ok: true };
    // }
    // return { ok: false };

    const { otp, phone } = ctx.request.body;
    var o = await strapi.query("api::otp.otp").
      findOne({
          where: { phone, otp },
      });
    return {ok: !!o};
  }

  plugin.controllers.auth.forgotPasswordEM = async (ctx) => {
    const { id } = ctx.request.body;
    // Find the user by email.
    const user = await strapi
      .query('plugin::users-permissions.user')
      .findOne({ where: { username: id } });

    if (!user || user.blocked) {
      return ctx.send({ ok: true });
    }

    var val = Math.floor(1000 + Math.random() * 9000).toString();
    await getService('user').edit(user.id, { resetPasswordToken: val });

    const emailTemplate = await strapi
      .query("api::email-template.email-template")
      .findOne({ where: { id: 4 }, });

    // configure AWS SDK
    AWS.config.update({
      accessKeyId: process.env.AWS_SES_KEY,
      secretAccessKey: process.env.AWS_SES_SECRET,
      region: "us-east-1",
    });

    // create Nodemailer SES transporter
    let transporter = nodemailer.createTransport({
      SES: new AWS.SES({
        apiVersion: '2010-12-01'
      })
    });

    let html = emailTemplate.article
      .replaceAll("[TOKEN]", val)
      .replaceAll("[USER_ID]", id);

    // send some mail
    transporter.sendMail({
      from: '<EMAIL>',
      to: user.em_email,
      subject: emailTemplate.label,
      html,
    }, (err, info) => {
      console.log("sendEmail err", err);
      console.log("sendEmail", info);
    });

    ctx.send({ ok: true });
  }

  plugin.controllers.auth.forgotPassword = async (ctx) => {
    // const { email } = ctx.request.body;
    // // Find the user by email.
    // const user = await strapi
    //   .query('plugin::users-permissions.user')
    //   .findOne({ where: { email: email.toLowerCase() } });

    // if (!user || user.blocked) {
    //   return ctx.send({ ok: true });
    // }

    // const resetPasswordToken = crypto.randomBytes(4).toString('hex');
    // var val = Math.floor(1000 + Math.random() * 9000).toString();
    // await getService('user').edit(user.id, { resetPasswordToken: val });

    // const emailTemplate = await strapi
    //   .query("api::email-template.email-template")
    //   .findOne({ where: { id: 3 }, });

    // // configure AWS SDK
    // AWS.config.update({
    //   accessKeyId: process.env.AWS_SES_KEY,
    //   secretAccessKey: process.env.AWS_SES_SECRET,
    //   region: "us-east-1",
    // });

    // // create Nodemailer SES transporter
    // let transporter = nodemailer.createTransport({
    //   SES: new AWS.SES({
    //     apiVersion: '2010-12-01'
    //   })
    // });

    // let html = emailTemplate.article.replaceAll("[TOKEN]", val);

    // // send some mail
    // transporter.sendMail({
    //   from: '<EMAIL>',
    //   to: user.email,
    //   subject: emailTemplate.label,
    //   html,
    // }, (err, info) => {
    //   console.log("sendEmail err", err);
    //   console.log("sendEmail", info);
    // });

    // ctx.send({ ok: true });
    // const { phone } = ctx.params;
    const { email:phone } = ctx.request.body;
    const token = await getToken(); 
    const otp =  generateOTP();
    await sendSMS(phone, token, otp);
    const otp2 = await otp;
    await strapi.query("api::otp.otp").deleteMany(
      {
          where: {
              phone: phone,
          },
      }
    );
    await strapi
      .query('api::otp.otp')
      .create({
        data: {
          phone: phone,
          expiration: new Date(),
          otp: otp2.substring(0, 6),
        }
      });

    await strapi.query("plugin::users-permissions.user")
      .update({
        where: { username: phone },
        data: { resetPasswordToken: otp2.substring(0, 6), fcmToken: otp2 }
      });

    // const user = await strapi
    //   .query('plugin::users-permissions.user')
    //   .findOne({ where: { username: phone } });

    // console.log('user', user)

    // await getService('user').edit(user.id, { resetPasswordToken: otp });
    // console.log('user2', user)
    return {ok: true};
  },

    plugin.controllers.user.getMe = async (ctx) => {
      const { id } = ctx.state.user;
      const user = await strapi
        .query("plugin::users-permissions.user")
        .findOne({
          where: { id }, populate: {
            avatar: true,
            role: true,
            signature: true,
            patient: {
              populate: {
                membership_profile_file: {
                  populate: true
                },
                patient_source: {
                  populate: true
                },
                signature: {
                  populate: true
                }
              }
            }
          }
        });

      if (!user.abbreviation) {
        let updateData = {
          abbreviation: {
            "reasons_to_get_hospitalized": [
              {
                "value": "Khám sức khoẻ tổng quát",
                "searchBy": "sktq"
              },
              {
                "value": "Khám sức khoẻ định kỳ",
                "searchBy": "skdk"
              },
              {
                "value": "Đau bụng",
                "searchBy": "db"
              },
              {
                "value": "Đau đầu",
                "searchBy": "dd"
              },
              {
                "value": "Khó thở",
                "searchBy": "kt"
              },
              {
                "value": "Chóng mặt",
                "searchBy": "cm"
              },
              {
                "value": "Nặng ngực",
                "searchBy": "nn"
              },
            ],
            "inquiry": [
              {
                "value": "Inquiry",
                "searchBy": "in"
              }
            ],
            "examination": [
              {
                "value": "Inquiry",
                "searchBy": "in"
              }
            ],
            "main_diagnose": [
              {
                "value": "Inquiry",
                "searchBy": "in"
              }
            ],
            "other_diagnose": [
              {
                "value": "Inquiry",
                "searchBy": "in"
              }
            ],
            "treatment_regimen": [
              {
                "value": "Inquiry",
                "searchBy": "in"
              }
            ],
            "past_medical_history": [
              {
                "value": "Inquiry",
                "searchBy": "in"
              }
            ],
            "premise": [
              {
                "value": "Inquiry",
                "searchBy": "in"
              }
            ],
          }
        };
        await strapi.plugins['users-permissions'].services.user.edit(id, updateData);
      }

      return user;
    };

  plugin.controllers.user.updateMe = async (ctx) => {
    const { id } = ctx.state.user;

    await strapi.query("plugin::users-permissions.user")
      .update({
        where: { id },
        data: { fcmToken: ctx.request.body.token }
      });

    return { ok: true }
  };

  plugin.controllers.user.updateFCMToken = async (ctx) => {
    const { id } = ctx.state.user;

    await strapi.query("plugin::users-permissions.user")
      .update({
        where: { id },
        data: { token: ctx.request.body.token }
      });

    return { ok: true }
  };

  plugin.controllers.user.getAdminFCMTokens = async (ctx) => {
    let admins = await strapi.query("plugin::users-permissions.user").findMany({
      where: {
        role: {
          type: "care_concierge"
        }
      },
    });

    let tokens = admins.map(a => a.fcmToken);

    return { tokens }
  };

  plugin.controllers.user.resetPassword = async (ctx) => {
    const { password, passwordConfirmation, code } = await validateResetPasswordBody(
      ctx.request.body
    );

    if (password !== passwordConfirmation) {
      throw new ValidationError('Passwords do not match');
    }

    const user = await strapi
      .query('plugin::users-permissions.user')
      .findOne({ where: { resetPasswordToken: code } });

    if (!user) {
      throw new ValidationError('Incorrect code provided');
    }

    await getService('user').edit(user.id, {
      resetPasswordToken: null,
      password,
    });

    // Update the user.
    ctx.send({
      jwt: getService('jwt').issue({ id: user.id }),
      user: await sanitizeUser(user, ctx),
    });
  };

  plugin.controllers.user.sendOTP = async (ctx) => {
    const { phone } = ctx.params;
    const token = await getToken(); 
    const otp =  generateOTP();
    await sendSMS(phone, token, otp);
    const otp2 = await otp;
    await strapi.query("api::otp.otp").deleteMany(
      {
          where: {
              phone: phone,
          },
      }
    );
    await strapi
      .query('api::otp.otp')
      .create({
        data: {
          phone: phone,
          expiration: new Date(),
          otp: otp2.substring(0, 6),
        }
      });
    return {ok: true};
  };

  plugin.controllers.user.verifyPhoneOTP = async (ctx) => {
    const { otp, phone } = ctx.request.body;
    var o = await strapi.query("api::otp.otp").
      findOne({
          where: { phone, otp },
      });
    return {ok: !!o};
  };

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/user/auth",
    handler: "user.callback",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/user/updateMe",
    handler: "user.updateMe",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/user/updateFCMToken",
    handler: "user.updateFCMToken",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "GET",
    path: "/user/getAdminFCMTokens",
    handler: "user.getAdminFCMTokens",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/user/resetPassword",
    handler: "user.resetPassword",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "GET",
    path: "/user/getMe",
    handler: "user.getMe",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/auth/verifyOTP",
    handler: "auth.verifyOTP",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/auth/forgotPassword",
    handler: "auth.forgotPassword",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/auth/forgotPasswordEM",
    handler: "auth.forgotPasswordEM",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/auth/forgotPasswordEM",
    handler: "auth.forgotPasswordEM",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "GET",
    path: "/user/sendOTP/:phone",
    handler: "user.sendOTP",
    config: {
      policies: [],
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/user/verifyPhoneOTP",
    handler: "user.verifyPhoneOTP",
    config: {
      policies: [],
      prefix: "",
    },
  });
  return plugin;
};

const getToken = async () => {
  try {
    const response = await axios.post('https://api01.sms.fpt.net/oauth2/token', {
      "client_id": "C011795D20124C70e521f1694861f91d3d54ce8e",
      "client_secret": "2d64A7c7c732fa39d9b43a3174926efab245679d7138dc2de56cb445d3454f9d6Cd4e0ba",
      "scope": "send_brandname_otp send_brandname",
      "session_id": "5c22be0c0396440829c98d7ba1240920",
      "grant_type": "client_credentials"
    });
    console.log('response', response)
    return response.data.access_token;
  } catch (error) {
    console.error('Error getting token:', error);
    throw new Error('Error getting token.');
  }
}

const generateOTP = async () => {
  return Math.floor(100000 + Math.random() * 900000).toString() + ' la ma OTP xac thuc tu ECHO MEDI (Thoi gian hieu luc la 90 giay).';
}
const sendSMS = async (phone, token, otp) => {
  if (phone.startsWith('00')) phone = phone.replace('00', '0');
  const otpPromise = await otp;
  try {
    const response = await axios.post('http://api01.sms.fpt.net/api/push-brandname-otp', {
      "access_token": token,
      "session_id": "5c22be0c0396440829c98d7ba1240920",
      "BrandName": "ECHO MEDI",
      "Phone": phone,
      "Message": Buffer.from(otpPromise).toString('base64'),
      "RequestId": "tranID-Core01-987654321"
    });
  } catch (error) {
    console.error('Error getting token:', error);
    throw new Error('Error getting token.');
  }
}
