{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true}, "confirmationToken": {"type": "string", "configurable": false, "private": true}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "cart": {"type": "relation", "relation": "oneToOne", "target": "api::cart.cart", "inversedBy": "users_permissions_user"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "check_ins": {"type": "relation", "relation": "oneToMany", "target": "api::check-in.check-in", "mappedBy": "user"}, "code": {"type": "string"}, "bookings": {"type": "relation", "relation": "oneToMany", "target": "api::booking.booking", "mappedBy": "user"}, "phone": {"type": "string"}, "gender": {"type": "string"}, "address": {"type": "json"}, "birthday": {"type": "date"}, "abbreviation": {"type": "json"}, "uid": {"type": "uid"}, "isDoctor": {"type": "boolean"}, "patient": {"type": "relation", "relation": "oneToOne", "target": "api::patient.patient", "mappedBy": "user"}, "fullname": {"type": "string"}, "fcmToken": {"type": "string"}, "token": {"type": "string"}, "avatar": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "signature": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "em_email": {"type": "email"}, "tasks": {"type": "relation", "relation": "manyToMany", "target": "api::task.task", "mappedBy": "members"}, "last_logged_in": {"type": "datetime"}}}