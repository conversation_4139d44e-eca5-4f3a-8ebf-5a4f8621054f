{"name": "my-projec-7", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi"}, "dependencies": {"@ckeditor/strapi-plugin-ckeditor": "^0.0.10", "@strapi/plugin-i18n": "4.13.7", "@strapi/plugin-users-permissions": "4.13.7", "@strapi/provider-email-amazon-ses": "^4.8.2", "@strapi/provider-email-nodemailer": "^4.5.4", "@strapi/provider-email-sendgrid": "^4.5.3", "@strapi/provider-email-sendmail": "^4.6.1", "@strapi/strapi": "4.13.7", "aws-sdk": "^2.1369.0", "better-sqlite3": "7.4.6", "dayjs": "^1.11.7", "firebase-admin": "^11.9.0", "lodash": "^4.17.21", "moment": "^2.29.4", "nodemailer": "^6.9.1", "postmark": "^3.0.15", "puppeteer": "^19.4.1", "react": "^18.2.0", "socket.io": "^4.6.0", "strapi-middleware-cache": "^2.1.8", "strapi-plugin-backup": "^1.1.2", "strapi-plugin-entity-relationship-chart": "^4.14.6", "strapi-plugin-excel-export-2024": "^1.0.7", "strapi-plugin-rest-cache": "^4.2.6", "twilio": "^4.19.0"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "ee96bce4-fc09-40e4-acfe-98bba7d93cd6"}, "engines": {"node": ">=14.19.1 <=18.x.x", "npm": ">=6.0.0"}, "license": "MIT"}