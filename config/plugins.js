module.exports = ({ env }) => ({
  email: {
    config: {
      provider: 'amazon-ses',
      providerOptions: {
        key: env('AWS_SES_KEY'),
        secret: env('AWS_SES_SECRET'),
        amazon: 'https://email.us-east-1.amazonaws.com',
      },
      settings: {
        defaultFrom: '<EMAIL>',
        defaultReplyTo: '<EMAIL>',
      },
    },
  },
  backup: {
    enabled: true,
    config: {
      cronSchedule: '0 0 * * *', // At minute 0 past every hour from 9 through 17
      storageService: 'aws-s3',
      awsAccessKeyId: '********************',
      awsSecretAccessKey: '2Xh5FcW1JgqlnKtbrcw+1zooqAA57t3H5KBi69SO',
      awsRegion: 'ap-southeast-1',
      awsS3Bucket: 'api.backup.em',
      sqlite3Executable: '/usr/bin/sqlite3',
      databaseDriver: 'sqlite',
      errorHandler: (error, strapi) => {
        console.log(error);
      },
    }
  },
  'users-permissions': {
    config: {
      jwt: {
        expiresIn: '7d',
      },
    },
  },
  // ...
});