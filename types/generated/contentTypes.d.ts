import type { Schema, Attribute } from '@strapi/strapi';

export interface AdminPermission extends Schema.CollectionType {
  collectionName: 'admin_permissions';
  info: {
    name: 'Permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    subject: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    properties: Attribute.JSON & Attribute.DefaultTo<{}>;
    conditions: Attribute.JSON & Attribute.DefaultTo<[]>;
    role: Attribute.Relation<'admin::permission', 'manyToOne', 'admin::role'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminUser extends Schema.CollectionType {
  collectionName: 'admin_users';
  info: {
    name: 'User';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    firstname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastname: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    username: Attribute.String;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.Private &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    registrationToken: Attribute.String & Attribute.Private;
    isActive: Attribute.Boolean &
      Attribute.Private &
      Attribute.DefaultTo<false>;
    roles: Attribute.Relation<'admin::user', 'manyToMany', 'admin::role'> &
      Attribute.Private;
    blocked: Attribute.Boolean & Attribute.Private & Attribute.DefaultTo<false>;
    preferedLanguage: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::user', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminRole extends Schema.CollectionType {
  collectionName: 'admin_roles';
  info: {
    name: 'Role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    code: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String;
    users: Attribute.Relation<'admin::role', 'manyToMany', 'admin::user'>;
    permissions: Attribute.Relation<
      'admin::role',
      'oneToMany',
      'admin::permission'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'admin::role', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface AdminApiToken extends Schema.CollectionType {
  collectionName: 'strapi_api_tokens';
  info: {
    name: 'Api Token';
    singularName: 'api-token';
    pluralName: 'api-tokens';
    displayName: 'Api Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    type: Attribute.Enumeration<['read-only', 'full-access', 'custom']> &
      Attribute.Required &
      Attribute.DefaultTo<'read-only'>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::api-token',
      'oneToMany',
      'admin::api-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminApiTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_api_token_permissions';
  info: {
    name: 'API Token Permission';
    description: '';
    singularName: 'api-token-permission';
    pluralName: 'api-token-permissions';
    displayName: 'API Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::api-token-permission',
      'manyToOne',
      'admin::api-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::api-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferToken extends Schema.CollectionType {
  collectionName: 'strapi_transfer_tokens';
  info: {
    name: 'Transfer Token';
    singularName: 'transfer-token';
    pluralName: 'transfer-tokens';
    displayName: 'Transfer Token';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    description: Attribute.String &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }> &
      Attribute.DefaultTo<''>;
    accessKey: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    lastUsedAt: Attribute.DateTime;
    permissions: Attribute.Relation<
      'admin::transfer-token',
      'oneToMany',
      'admin::transfer-token-permission'
    >;
    expiresAt: Attribute.DateTime;
    lifespan: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface AdminTransferTokenPermission extends Schema.CollectionType {
  collectionName: 'strapi_transfer_token_permissions';
  info: {
    name: 'Transfer Token Permission';
    description: '';
    singularName: 'transfer-token-permission';
    pluralName: 'transfer-token-permissions';
    displayName: 'Transfer Token Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 1;
      }>;
    token: Attribute.Relation<
      'admin::transfer-token-permission',
      'manyToOne',
      'admin::transfer-token'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'admin::transfer-token-permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFile extends Schema.CollectionType {
  collectionName: 'files';
  info: {
    singularName: 'file';
    pluralName: 'files';
    displayName: 'File';
    description: '';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String & Attribute.Required;
    alternativeText: Attribute.String;
    caption: Attribute.String;
    width: Attribute.Integer;
    height: Attribute.Integer;
    formats: Attribute.JSON;
    hash: Attribute.String & Attribute.Required;
    ext: Attribute.String;
    mime: Attribute.String & Attribute.Required;
    size: Attribute.Decimal & Attribute.Required;
    url: Attribute.String & Attribute.Required;
    previewUrl: Attribute.String;
    provider: Attribute.String & Attribute.Required;
    provider_metadata: Attribute.JSON;
    related: Attribute.Relation<'plugin::upload.file', 'morphToMany'>;
    folder: Attribute.Relation<
      'plugin::upload.file',
      'manyToOne',
      'plugin::upload.folder'
    > &
      Attribute.Private;
    folderPath: Attribute.String &
      Attribute.Required &
      Attribute.Private &
      Attribute.SetMinMax<{
        min: 1;
      }>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.file',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUploadFolder extends Schema.CollectionType {
  collectionName: 'upload_folders';
  info: {
    singularName: 'folder';
    pluralName: 'folders';
    displayName: 'Folder';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<{
        min: 1;
      }>;
    pathId: Attribute.Integer & Attribute.Required & Attribute.Unique;
    parent: Attribute.Relation<
      'plugin::upload.folder',
      'manyToOne',
      'plugin::upload.folder'
    >;
    children: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.folder'
    >;
    files: Attribute.Relation<
      'plugin::upload.folder',
      'oneToMany',
      'plugin::upload.file'
    >;
    path: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMax<{
        min: 1;
      }>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::upload.folder',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsPermission
  extends Schema.CollectionType {
  collectionName: 'up_permissions';
  info: {
    name: 'permission';
    description: '';
    singularName: 'permission';
    pluralName: 'permissions';
    displayName: 'Permission';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    action: Attribute.String & Attribute.Required;
    role: Attribute.Relation<
      'plugin::users-permissions.permission',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.permission',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsRole extends Schema.CollectionType {
  collectionName: 'up_roles';
  info: {
    name: 'role';
    description: '';
    singularName: 'role';
    pluralName: 'roles';
    displayName: 'Role';
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    description: Attribute.String;
    type: Attribute.String & Attribute.Unique;
    permissions: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.permission'
    >;
    users: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.role',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginUsersPermissionsUser extends Schema.CollectionType {
  collectionName: 'up_users';
  info: {
    name: 'user';
    description: '';
    singularName: 'user';
    pluralName: 'users';
    displayName: 'User';
  };
  options: {
    draftAndPublish: false;
  };
  attributes: {
    username: Attribute.String &
      Attribute.Required &
      Attribute.Unique &
      Attribute.SetMinMaxLength<{
        minLength: 3;
      }>;
    email: Attribute.Email &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    provider: Attribute.String;
    password: Attribute.Password &
      Attribute.Private &
      Attribute.SetMinMaxLength<{
        minLength: 6;
      }>;
    resetPasswordToken: Attribute.String & Attribute.Private;
    confirmationToken: Attribute.String & Attribute.Private;
    confirmed: Attribute.Boolean & Attribute.DefaultTo<false>;
    blocked: Attribute.Boolean & Attribute.DefaultTo<false>;
    role: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToOne',
      'plugin::users-permissions.role'
    >;
    cart: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'api::cart.cart'
    >;
    firstName: Attribute.String;
    lastName: Attribute.String;
    check_ins: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToMany',
      'api::check-in.check-in'
    >;
    code: Attribute.String;
    bookings: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToMany',
      'api::booking.booking'
    >;
    phone: Attribute.String;
    gender: Attribute.String;
    address: Attribute.JSON;
    birthday: Attribute.Date;
    abbreviation: Attribute.JSON;
    uid: Attribute.UID;
    isDoctor: Attribute.Boolean;
    patient: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'api::patient.patient'
    >;
    fullname: Attribute.String;
    fcmToken: Attribute.String;
    token: Attribute.String;
    avatar: Attribute.Media;
    signature: Attribute.Media;
    em_email: Attribute.Email;
    tasks: Attribute.Relation<
      'plugin::users-permissions.user',
      'manyToMany',
      'api::task.task'
    >;
    last_logged_in: Attribute.DateTime;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::users-permissions.user',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface PluginI18NLocale extends Schema.CollectionType {
  collectionName: 'i18n_locale';
  info: {
    singularName: 'locale';
    pluralName: 'locales';
    collectionName: 'locales';
    displayName: 'Locale';
    description: '';
  };
  options: {
    draftAndPublish: false;
  };
  pluginOptions: {
    'content-manager': {
      visible: false;
    };
    'content-type-builder': {
      visible: false;
    };
  };
  attributes: {
    name: Attribute.String &
      Attribute.SetMinMax<{
        min: 1;
        max: 50;
      }>;
    code: Attribute.String & Attribute.Unique;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'plugin::i18n.locale',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiAboutAbout extends Schema.SingleType {
  collectionName: 'abouts';
  info: {
    singularName: 'about';
    pluralName: 'abouts';
    displayName: 'About';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    vi: Attribute.RichText;
    en: Attribute.RichText;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::about.about',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::about.about',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiArticleArticle extends Schema.CollectionType {
  collectionName: 'articles';
  info: {
    singularName: 'article';
    pluralName: 'articles';
    displayName: 'Article';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    slug: Attribute.UID<'api::article.article', 'title'>;
    category: Attribute.Enumeration<
      [
        'hoat_dong',
        'tintuc',
        'tintuc_wellness',
        'tintuc_primary_care',
        'tintuc_chronic_diseases',
        'tintuc_pharmacy',
        'video',
        'video_about_us',
        'video_medical_news',
        'video_events',
        'video_livestream',
        'tintuc_pediatrics',
        'tintuc_newspapers'
      ]
    >;
    content: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor.CKEditor',
        {
          output: 'HTML';
          preset: 'rich';
        }
      >;
    cover: Attribute.Media;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::article.article',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::article.article',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBannerBanner extends Schema.SingleType {
  collectionName: 'banners';
  info: {
    singularName: 'banner';
    pluralName: 'banners';
    displayName: 'Banner';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    images: Attribute.Media;
    isHidden: Attribute.Boolean & Attribute.DefaultTo<false>;
    urls: Attribute.RichText;
    links: Attribute.Text;
    en_images: Attribute.Media;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::banner.banner',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::banner.banner',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBlogBlog extends Schema.CollectionType {
  collectionName: 'blogs';
  info: {
    singularName: 'blog';
    pluralName: 'blogs';
    displayName: 'Blog';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  pluginOptions: {
    versions: {
      versioned: true;
    };
  };
  attributes: {
    article: Attribute.RichText;
    test: Attribute.RichText;
    label: Attribute.String;
    slug: Attribute.UID<'api::blog.blog', 'label'>;
    image: Attribute.Media;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::blog.blog', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::blog.blog', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiBoardBoard extends Schema.CollectionType {
  collectionName: 'boards';
  info: {
    singularName: 'board';
    pluralName: 'boards';
    displayName: 'Board';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    users: Attribute.Relation<
      'api::board.board',
      'oneToMany',
      'plugin::users-permissions.user'
    >;
    lists: Attribute.Relation<
      'api::board.board',
      'oneToMany',
      'api::list.list'
    >;
    tasks: Attribute.Relation<
      'api::board.board',
      'oneToMany',
      'api::task.task'
    >;
    coverPhoto: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::board.board',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::board.board',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBookingBooking extends Schema.CollectionType {
  collectionName: 'bookings';
  info: {
    singularName: 'booking';
    pluralName: 'bookings';
    displayName: 'Booking';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    timeSession: Attribute.String;
    contactReceiver: Attribute.String;
    contactPhoneNumber: Attribute.String;
    contactEmail: Attribute.String;
    contactAddress: Attribute.JSON;
    status: Attribute.Enumeration<
      ['scheduled', 'confirmed', 'finished', 'cancelled', 'postpone', 'waiting']
    >;
    note: Attribute.Text;
    callToRemind: Attribute.Boolean & Attribute.DefaultTo<false>;
    bookingDate: Attribute.DateTime;
    code: Attribute.String;
    createdByAdmin: Attribute.Boolean;
    scheduleTreatmentTimes: Attribute.Integer;
    treatmentTime: Attribute.Integer;
    user: Attribute.Relation<
      'api::booking.booking',
      'manyToOne',
      'plugin::users-permissions.user'
    >;
    patient: Attribute.Relation<
      'api::booking.booking',
      'oneToOne',
      'api::patient.patient'
    >;
    medical_record: Attribute.Relation<
      'api::booking.booking',
      'oneToOne',
      'api::medical-record.medical-record'
    >;
    branch: Attribute.Enumeration<['q7', 'q2', 'binhduong', 'bd', 'all']>;
    dontShowOnCalendar: Attribute.Boolean;
    membership: Attribute.String;
    latitude: Attribute.Decimal;
    longitude: Attribute.Decimal;
    type: Attribute.Enumeration<['at_clinic', 'home_visit', 'telemedicine']>;
    contactFullName: Attribute.String;
    cc_note: Attribute.String;
    paid: Attribute.Boolean;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::booking.booking',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::booking.booking',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBundleExamBundleExam extends Schema.CollectionType {
  collectionName: 'bundle_exams';
  info: {
    singularName: 'bundle-exam';
    pluralName: 'bundle-exams';
    displayName: 'Bundle Exam';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    examinations: Attribute.Relation<
      'api::bundle-exam.bundle-exam',
      'oneToMany',
      'api::examination.examination'
    >;
    title: Attribute.String;
    en_title: Attribute.String;
    slug: Attribute.UID<'api::bundle-exam.bundle-exam', 'title'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bundle-exam.bundle-exam',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bundle-exam.bundle-exam',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBundleSericesJsonBundleSericesJson
  extends Schema.SingleType {
  collectionName: 'bundle_serices_jsons';
  info: {
    singularName: 'bundle-serices-json';
    pluralName: 'bundle-serices-jsons';
    displayName: 'Bundle Serices Json';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    data: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bundle-serices-json.bundle-serices-json',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bundle-serices-json.bundle-serices-json',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiBundleServiceUsageBundleServiceUsage
  extends Schema.CollectionType {
  collectionName: 'bundle_service_usages';
  info: {
    singularName: 'bundle-service-usage';
    pluralName: 'bundle-service-usages';
    displayName: 'Bundle Service Usage';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    service_bundle: Attribute.Relation<
      'api::bundle-service-usage.bundle-service-usage',
      'oneToOne',
      'api::service-bundle.service-bundle'
    >;
    patient: Attribute.Relation<
      'api::bundle-service-usage.bundle-service-usage',
      'oneToOne',
      'api::patient.patient'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::bundle-service-usage.bundle-service-usage',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::bundle-service-usage.bundle-service-usage',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCameraCamera extends Schema.SingleType {
  collectionName: 'cameras';
  info: {
    singularName: 'camera';
    pluralName: 'cameras';
    displayName: 'Camera';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    access_token: Attribute.String;
    refresh_token: Attribute.String;
    client_id: Attribute.String;
    client_secret: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::camera.camera',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::camera.camera',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCartCart extends Schema.CollectionType {
  collectionName: 'carts';
  info: {
    singularName: 'cart';
    pluralName: 'carts';
    displayName: 'Cart';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    products: Attribute.Relation<
      'api::cart.cart',
      'manyToMany',
      'api::product.product'
    >;
    users_permissions_user: Attribute.Relation<
      'api::cart.cart',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    cart_lines: Attribute.Relation<
      'api::cart.cart',
      'oneToMany',
      'api::cart-line.cart-line'
    >;
    text: Attribute.String;
    order: Attribute.Relation<'api::cart.cart', 'oneToOne', 'api::order.order'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::cart.cart', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::cart.cart', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiCartLineCartLine extends Schema.CollectionType {
  collectionName: 'cart_lines';
  info: {
    singularName: 'cart-line';
    pluralName: 'cart-lines';
    displayName: 'Cart Line';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    product: Attribute.Relation<
      'api::cart-line.cart-line',
      'oneToOne',
      'api::product.product'
    >;
    cart: Attribute.Relation<
      'api::cart-line.cart-line',
      'manyToOne',
      'api::cart.cart'
    >;
    service: Attribute.Relation<
      'api::cart-line.cart-line',
      'oneToOne',
      'api::service.service'
    >;
    quantity: Attribute.Integer;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::cart-line.cart-line',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::cart-line.cart-line',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCharacterCharacter extends Schema.CollectionType {
  collectionName: 'characters';
  info: {
    singularName: 'character';
    pluralName: 'characters';
    displayName: 'Character';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    uid: Attribute.UID;
    data: Attribute.JSON;
    sub_expiration_date: Attribute.Date;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::character.character',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::character.character',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiCheckInCheckIn extends Schema.CollectionType {
  collectionName: 'check_ins';
  info: {
    singularName: 'check-in';
    pluralName: 'check-ins';
    displayName: 'CheckIn';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    checkedoutAt: Attribute.DateTime;
    metadata: Attribute.JSON;
    personID: Attribute.String;
    status: Attribute.Enumeration<
      ['waiting', 'progress', 'done', 'paid', 'confirmed']
    > &
      Attribute.DefaultTo<'waiting'>;
    user: Attribute.Relation<
      'api::check-in.check-in',
      'manyToOne',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::check-in.check-in',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::check-in.check-in',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiChronicServiceChronicService extends Schema.CollectionType {
  collectionName: 'chronic_services';
  info: {
    singularName: 'chronic-service';
    pluralName: 'chronic-services';
    displayName: 'Chronic Service';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    label: Attribute.String;
    Services: Attribute.Component<'service.service', true>;
    price: Attribute.Integer;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::chronic-service.chronic-service',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::chronic-service.chronic-service',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiConversationConversation extends Schema.CollectionType {
  collectionName: 'conversations';
  info: {
    singularName: 'conversation';
    pluralName: 'conversations';
    displayName: 'Conversation';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    first_person: Attribute.Relation<
      'api::conversation.conversation',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    second_person: Attribute.Relation<
      'api::conversation.conversation',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    data: Attribute.JSON;
    conversation_queue: Attribute.Relation<
      'api::conversation.conversation',
      'oneToOne',
      'api::conversation-queue.conversation-queue'
    >;
    latest_message: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::conversation.conversation',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::conversation.conversation',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiConversationQueueConversationQueue
  extends Schema.CollectionType {
  collectionName: 'conversation_queues';
  info: {
    singularName: 'conversation-queue';
    pluralName: 'conversation-queues';
    displayName: 'Conversation Queue';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    user: Attribute.Relation<
      'api::conversation-queue.conversation-queue',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    data: Attribute.JSON;
    conversation: Attribute.Relation<
      'api::conversation-queue.conversation-queue',
      'oneToOne',
      'api::conversation.conversation'
    >;
    latest_message: Attribute.String;
    second_person: Attribute.Relation<
      'api::conversation-queue.conversation-queue',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    waiting: Attribute.Boolean;
    supporter: Attribute.Enumeration<['doctor', 'care-concierge']>;
    branch: Attribute.String;
    user_seen: Attribute.Boolean;
    second_person_seen: Attribute.Boolean;
    latest: Attribute.DateTime;
    note: Attribute.String;
    status: Attribute.Enumeration<['incomplete', 'complete']>;
    user_seen_date: Attribute.DateTime;
    second_person_seen_date: Attribute.DateTime;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::conversation-queue.conversation-queue',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::conversation-queue.conversation-queue',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiDiscountSettingDiscountSetting extends Schema.SingleType {
  collectionName: 'discount_settings';
  info: {
    singularName: 'discount-setting';
    pluralName: 'discount-settings';
    displayName: 'Discount Setting';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    reasons: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::discount-setting.discount-setting',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::discount-setting.discount-setting',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiDrugDrug extends Schema.CollectionType {
  collectionName: 'drugs';
  info: {
    singularName: 'drug';
    pluralName: 'drugs';
    displayName: 'Drug';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    code: Attribute.String;
    label: Attribute.String;
    type: Attribute.String;
    ingredient: Attribute.String;
    stock: Attribute.Integer;
    unit: Attribute.String;
    label_i: Attribute.String;
    ingredient_i: Attribute.String;
    branch: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::drug.drug', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::drug.drug', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiEmailTemplateEmailTemplate extends Schema.CollectionType {
  collectionName: 'email_templates';
  info: {
    singularName: 'email-template';
    pluralName: 'email-templates';
    displayName: 'Email Template';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    article: Attribute.RichText;
    label: Attribute.String;
    defaultSendFromEmail: Attribute.String;
    design: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::email-template.email-template',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::email-template.email-template',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiEventLogEventLog extends Schema.CollectionType {
  collectionName: 'event_logs';
  info: {
    singularName: 'event-log';
    pluralName: 'event-logs';
    displayName: 'Event Log';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    data: Attribute.JSON;
    type: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::event-log.event-log',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::event-log.event-log',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiExaminationExamination extends Schema.CollectionType {
  collectionName: 'examinations';
  info: {
    singularName: 'examination';
    pluralName: 'examinations';
    displayName: 'Examination';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    en_title: Attribute.String;
    image: Attribute.Media;
    desc: Attribute.RichText;
    en_desc: Attribute.RichText;
    bundle_exam: Attribute.Relation<
      'api::examination.examination',
      'manyToOne',
      'api::bundle-exam.bundle-exam'
    >;
    slug: Attribute.UID<'api::examination.examination', 'title'>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::examination.examination',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::examination.examination',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiGlobalSettingGlobalSetting extends Schema.SingleType {
  collectionName: 'global_settings';
  info: {
    singularName: 'global-setting';
    pluralName: 'global-settings';
    displayName: 'Global Setting';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    data: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::global-setting.global-setting',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::global-setting.global-setting',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiIndexImageIndexImage extends Schema.SingleType {
  collectionName: 'index_images';
  info: {
    singularName: 'index-image';
    pluralName: 'index-images';
    displayName: 'Index Image';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    image: Attribute.Media;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::index-image.index-image',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::index-image.index-image',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiInvoiceInvoice extends Schema.CollectionType {
  collectionName: 'invoices';
  info: {
    singularName: 'invoice';
    pluralName: 'invoices';
    displayName: 'invoice';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    uid: Attribute.UID;
    booking: Attribute.Relation<
      'api::invoice.invoice',
      'oneToOne',
      'api::booking.booking'
    >;
    data: Attribute.JSON;
    idu: Attribute.BigInteger;
    cashier_in_charge: Attribute.Relation<
      'api::invoice.invoice',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::invoice.invoice',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::invoice.invoice',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiListList extends Schema.CollectionType {
  collectionName: 'lists';
  info: {
    singularName: 'list';
    pluralName: 'lists';
    displayName: 'List';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    uid: Attribute.UID;
    title: Attribute.String;
    board: Attribute.Relation<
      'api::list.list',
      'manyToOne',
      'api::board.board'
    >;
    tasks: Attribute.Relation<'api::list.list', 'oneToMany', 'api::task.task'>;
    taskIds: Attribute.JSON;
    approvalType: Attribute.String;
    defaultAssigments: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::list.list', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::list.list', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiLogLog extends Schema.CollectionType {
  collectionName: 'logs';
  info: {
    singularName: 'log';
    pluralName: 'logs';
    displayName: 'Log';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    message: Attribute.Text;
    data: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::log.log', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::log.log', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiMedicalRecordMedicalRecord extends Schema.CollectionType {
  collectionName: 'medical_records';
  info: {
    singularName: 'medical-record';
    pluralName: 'medical-records';
    displayName: 'Medical Record';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    circuit: Attribute.Integer;
    temperature: Attribute.Decimal;
    blood_pressure: Attribute.Decimal;
    respiratory_rate: Attribute.Decimal;
    height: Attribute.Decimal;
    weight: Attribute.Decimal;
    bmi: Attribute.Decimal;
    spo2: Attribute.Decimal;
    reasons_to_get_hospitalized: Attribute.String;
    diagnose: Attribute.String;
    treatment_regimen: Attribute.String;
    examination: Attribute.String;
    inquiry: Attribute.String;
    user: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    services: Attribute.JSON;
    bundle_services: Attribute.JSON;
    total: Attribute.Decimal;
    patient: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'api::patient.patient'
    >;
    booking: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'api::booking.booking'
    >;
    blood_pressure2: Attribute.Integer;
    past_medical_history: Attribute.String;
    prescription: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'api::prescription.prescription'
    >;
    testResults: Attribute.JSON;
    premise: Attribute.String;
    general_examination: Attribute.String;
    main_diagnose: Attribute.String;
    other_diagnose: Attribute.String;
    membership: Attribute.String;
    uid: Attribute.String;
    doctor_in_charge: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    references: Attribute.JSON;
    clinique_services: Attribute.JSON;
    cc_note: Attribute.String;
    prescriptions: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToMany',
      'api::prescription.prescription'
    >;
    noi_khoa: Attribute.Text;
    ngoai_khoa: Attribute.Text;
    san_khoa: Attribute.Text;
    tiem_chung: Attribute.Text;
    di_ung: Attribute.Text;
    thoi_quen: Attribute.Text;
    nguy_co_khac: Attribute.Text;
    van_de_khac: Attribute.Text;
    tien_can_gia_dinh: Attribute.Text;
    tong_quat: Attribute.Text;
    tim_mach: Attribute.Text;
    ho_hap: Attribute.Text;
    tieu_hoa_tiet_nieu: Attribute.Text;
    co_xuong_khop: Attribute.Text;
    than_kinh: Attribute.Text;
    san_phu_khoa: Attribute.Text;
    mat_tai_mui_hong: Attribute.Text;
    co_quan_khac: Attribute.Text;
    cac_thang_diem_can_danh_gia: Attribute.Text;
    dinh_duong: Attribute.Text;
    ket_qua_cls: Attribute.Text;
    chan_doan: Attribute.Text;
    blood_pressure_1: Attribute.Integer;
    blood_pressure2_1: Attribute.Integer;
    status: Attribute.String;
    cc_in_charge: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    nurse_in_charge: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    cashier_in_charge: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    edit_by: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    session: Attribute.Text;
    nguoi_lien_he_luc_khan_cap: Attribute.Text;
    cac_luong_gia_va_trac_nghiem_da_thuc_hien: Attribute.Text;
    ket_qua_trac_nghiem: Attribute.Text;
    ly_do_den_tham_van: Attribute.Text;
    hanh_vi_quan_sat: Attribute.Text;
    mo_ta_trieu_chung: Attribute.Text;
    thong_tin_nen_tang_boi_canh_lien_quan: Attribute.Text;
    yeu_to_khoi_phat: Attribute.Text;
    yeu_to_bao_ve: Attribute.Text;
    yeu_to_kich_hoat: Attribute.Text;
    yeu_to_duy_tri: Attribute.Text;
    anh_huong_toi_cuoc_song: Attribute.Text;
    cach_giai_quyet_van_de_da_su_dung: Attribute.Text;
    nhu_cau_va_muc_tieu_tham_van: Attribute.Text;
    tom_tat_van_de: Attribute.Text;
    ke_hoach_tham_van: Attribute.JSON;
    cac_giay_to_lien_quan: Attribute.JSON;
    ghi_chu_cua_cvtl: Attribute.Text;
    is_mental_health_mr: Attribute.Boolean;
    counselor_in_charge: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    chronic_services: Attribute.JSON;
    phat_trien: Attribute.Text;
    benh_ly: Attribute.Text;
    is_pediatric_mr: Attribute.Boolean;
    chien_luoc_can_thiep: Attribute.Text;
    theo_doi_sau_phien: Attribute.Text;
    lich_su_nghien_ngap: Attribute.Text;
    is_pediatric_mental_health_mr: Attribute.Boolean;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::medical-record.medical-record',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMedicalServiceMedicalService extends Schema.CollectionType {
  collectionName: 'medical_services';
  info: {
    singularName: 'medical-service';
    pluralName: 'medical-services';
    displayName: 'Medical Service';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    label: Attribute.String;
    code: Attribute.String;
    host: Attribute.String;
    price: Attribute.Integer;
    group_service: Attribute.String;
    service_bundles: Attribute.Relation<
      'api::medical-service.medical-service',
      'manyToMany',
      'api::service-bundle.service-bundle'
    >;
    Locations: Attribute.Component<
      'medical-service-setting.medical-service-setting',
      true
    >;
    membership_discount: Attribute.Component<'membership-discount.membership-discount'>;
    label_i: Attribute.String;
    tags: Attribute.JSON;
    is_mental_health_service: Attribute.Boolean;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::medical-service.medical-service',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::medical-service.medical-service',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMedicalServiceUsageMedicalServiceUsage
  extends Schema.CollectionType {
  collectionName: 'medical_service_usages';
  info: {
    singularName: 'medical-service-usage';
    pluralName: 'medical-service-usages';
    displayName: 'MedicalServiceUsage';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    medical_service: Attribute.Relation<
      'api::medical-service-usage.medical-service-usage',
      'oneToOne',
      'api::medical-service.medical-service'
    >;
    patient: Attribute.Relation<
      'api::medical-service-usage.medical-service-usage',
      'oneToOne',
      'api::patient.patient'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::medical-service-usage.medical-service-usage',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::medical-service-usage.medical-service-usage',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMedicineMedicine extends Schema.CollectionType {
  collectionName: 'medicines';
  info: {
    singularName: 'medicine';
    pluralName: 'medicines';
    displayName: 'Medicine';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    label: Attribute.String;
    image: Attribute.Media;
    desc: Attribute.RichText;
    products: Attribute.Relation<
      'api::medicine.medicine',
      'manyToMany',
      'api::product.product'
    >;
    en_products: Attribute.Relation<
      'api::medicine.medicine',
      'manyToMany',
      'api::product.product'
    >;
    en_label: Attribute.String;
    en_desc: Attribute.RichText;
    drug: Attribute.Relation<
      'api::medicine.medicine',
      'oneToOne',
      'api::drug.drug'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::medicine.medicine',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::medicine.medicine',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiMembershipCardMembershipCard extends Schema.CollectionType {
  collectionName: 'membership_cards';
  info: {
    singularName: 'membership-card';
    pluralName: 'membership-cards';
    displayName: 'Card';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    code: Attribute.String;
    remainValue: Attribute.BigInteger & Attribute.DefaultTo<'0'>;
    status: Attribute.Enumeration<['active', 'suspended', 'completed']> &
      Attribute.DefaultTo<'active'>;
    type: Attribute.Enumeration<['member-card', 'service-card']>;
    usageLimit: Attribute.Integer;
    lastUsedAt: Attribute.DateTime;
    expiredDate: Attribute.Date;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::membership-card.membership-card',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::membership-card.membership-card',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiOrderOrder extends Schema.CollectionType {
  collectionName: 'orders';
  info: {
    singularName: 'order';
    pluralName: 'orders';
    displayName: 'Order';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    orderedDate: Attribute.DateTime;
    contactReceiver: Attribute.String;
    contactPhoneNumber: Attribute.String;
    contactEmail: Attribute.String;
    contactAddress: Attribute.JSON;
    subTotal: Attribute.BigInteger;
    promotion: Attribute.BigInteger;
    shippingFee: Attribute.BigInteger;
    tax: Attribute.Integer;
    total: Attribute.BigInteger;
    products: Attribute.JSON;
    image: Attribute.Media;
    paymentMethod: Attribute.String;
    code: Attribute.String;
    status: Attribute.Enumeration<['draft', 'ordered', 'done', 'canceled']> &
      Attribute.DefaultTo<'ordered'>;
    dynamicLink: Attribute.String;
    users_permissions_user: Attribute.Relation<
      'api::order.order',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    cart: Attribute.Relation<'api::order.order', 'oneToOne', 'api::cart.cart'>;
    cart1: Attribute.Relation<'api::order.order', 'oneToOne', 'api::cart.cart'>;
    num_of_prod: Attribute.Integer;
    vnp_payload: Attribute.JSON;
    vnp_payment_url_params: Attribute.JSON;
    reference_id: Attribute.UID;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::order.order',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::order.order',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiOtpOtp extends Schema.CollectionType {
  collectionName: 'otps';
  info: {
    singularName: 'otp';
    pluralName: 'otps';
    displayName: 'OTP';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    phone: Attribute.String;
    expiration: Attribute.DateTime;
    otp: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::otp.otp', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::otp.otp', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiPackagePackage extends Schema.CollectionType {
  collectionName: 'packages';
  info: {
    singularName: 'package';
    pluralName: 'packages';
    displayName: 'Package';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    label: Attribute.Text;
    slug: Attribute.UID<'api::package.package', 'label'>;
    sub_packages: Attribute.Relation<
      'api::package.package',
      'oneToMany',
      'api::sub-package.sub-package'
    >;
    hero_img: Attribute.Media;
    desc: Attribute.RichText;
    en_label: Attribute.String;
    en_desc: Attribute.RichText;
    type: Attribute.Enumeration<['other', 'echomedi_gen', 'gen']> &
      Attribute.DefaultTo<'other'>;
    label_lv1: Attribute.String;
    label_lv2: Attribute.String;
    en_label_lv1: Attribute.String;
    en_label_lv2: Attribute.String;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::package.package',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::package.package',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPatientPatient extends Schema.CollectionType {
  collectionName: 'patients';
  info: {
    singularName: 'patient';
    pluralName: 'patients';
    displayName: 'Patient';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    full_name: Attribute.String;
    birthday: Attribute.DateTime;
    gender: Attribute.String;
    job: Attribute.String;
    address: Attribute.JSON;
    phone: Attribute.String;
    email: Attribute.String;
    full_name_i: Attribute.String;
    membership: Attribute.String;
    uid: Attribute.UID;
    user: Attribute.Relation<
      'api::patient.patient',
      'oneToOne',
      'plugin::users-permissions.user'
    >;
    relative_phone: Attribute.String;
    relationships: Attribute.Component<'relationship.relationship', true>;
    internal: Attribute.String;
    start_date_membership: Attribute.Date;
    membership_profile_file: Attribute.Media;
    source: Attribute.Enumeration<['app', 'web', 'app_be', 'other']> &
      Attribute.DefaultTo<'other'>;
    patient_source: Attribute.Relation<
      'api::patient.patient',
      'oneToOne',
      'api::patient-source.patient-source'
    >;
    discount: Attribute.JSON;
    currentHealth: Attribute.JSON;
    remaining_services: Attribute.Component<'service.service', true>;
    rem_services: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::patient.patient',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::patient.patient',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPatientSourcePatientSource extends Schema.CollectionType {
  collectionName: 'patient_sources';
  info: {
    singularName: 'patient-source';
    pluralName: 'patient-sources';
    displayName: 'Patient Source';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    label: Attribute.String;
    value: Attribute.String;
    image: Attribute.Media;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::patient-source.patient-source',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::patient-source.patient-source',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiPrescriptionPrescription extends Schema.CollectionType {
  collectionName: 'prescriptions';
  info: {
    singularName: 'prescription';
    pluralName: 'prescriptions';
    displayName: 'Prescription';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    medicalRecord: Attribute.Relation<
      'api::prescription.prescription',
      'oneToOne',
      'api::medical-record.medical-record'
    >;
    message: Attribute.Text;
    reExaminationDate: Attribute.DateTime;
    Drugs: Attribute.Component<'prescription-drug.prescription-drug', true>;
    additional_message: Attribute.String;
    additional_drugs: Attribute.Component<
      'prescription-drug.prescription-drug',
      true
    >;
    medical_record: Attribute.Relation<
      'api::prescription.prescription',
      'manyToOne',
      'api::medical-record.medical-record'
    >;
    uid: Attribute.UID;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::prescription.prescription',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::prescription.prescription',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiProductProduct extends Schema.CollectionType {
  collectionName: 'products';
  info: {
    singularName: 'product';
    pluralName: 'products';
    displayName: 'Product';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  pluginOptions: {
    i18n: {
      localized: true;
    };
  };
  attributes: {
    label: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    image: Attribute.Media &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    desc: Attribute.RichText &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    price: Attribute.Integer &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    medicines: Attribute.Relation<
      'api::product.product',
      'manyToMany',
      'api::medicine.medicine'
    >;
    slug: Attribute.UID<'api::product.product', 'label'>;
    carts: Attribute.Relation<
      'api::product.product',
      'manyToMany',
      'api::cart.cart'
    >;
    en_label: Attribute.String &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    en_desc: Attribute.RichText &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    en_medicines: Attribute.Relation<
      'api::product.product',
      'manyToMany',
      'api::medicine.medicine'
    >;
    tags: Attribute.JSON &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    en_slug: Attribute.UID<'api::product.product', 'en_label'> &
      Attribute.SetPluginOptions<{
        i18n: {
          localized: true;
        };
      }>;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::product.product',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::product.product',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    localizations: Attribute.Relation<
      'api::product.product',
      'oneToMany',
      'api::product.product'
    >;
    locale: Attribute.String;
  };
}

export interface ApiReasonListReasonList extends Schema.SingleType {
  collectionName: 'reason_lists';
  info: {
    singularName: 'reason-list';
    pluralName: 'reason-lists';
    displayName: 'reason_list';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    reasons: Attribute.JSON;
    reasons_en: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::reason-list.reason-list',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::reason-list.reason-list',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiRoomRoom extends Schema.CollectionType {
  collectionName: 'rooms';
  info: {
    singularName: 'room';
    pluralName: 'rooms';
    displayName: 'Room';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    uuid: Attribute.UID;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::room.room', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::room.room', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiServiceService extends Schema.CollectionType {
  collectionName: 'services';
  info: {
    singularName: 'service';
    pluralName: 'services';
    displayName: 'Service';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    label: Attribute.Text;
    slug: Attribute.UID<'api::service.service', 'label'>;
    desc: Attribute.Text;
    price: Attribute.BigInteger;
    detail: Attribute.RichText;
    sub_package: Attribute.Relation<
      'api::service.service',
      'manyToOne',
      'api::sub-package.sub-package'
    >;
    show_price: Attribute.Boolean;
    show_buy_btn: Attribute.Boolean;
    show_learn_more: Attribute.Boolean;
    en_label: Attribute.Text;
    en_desc: Attribute.String;
    en_detail: Attribute.RichText;
    show_additional_fee: Attribute.Boolean;
    show_inquiry_form: Attribute.Boolean;
    show_booking_btn: Attribute.Boolean;
    detail_mobile: Attribute.RichText;
    en_detail_mobile: Attribute.RichText;
    benefit: Attribute.String;
    properties: Attribute.JSON;
    genetica_image: Attribute.Media;
    genetica_pdf: Attribute.String;
    en_benefit: Attribute.String;
    en_properties: Attribute.JSON;
    specification: Attribute.JSON;
    en_specification: Attribute.JSON;
    type: Attribute.Enumeration<
      ['other', 'gen_detail', 'membership', 'policy']
    >;
    label_web: Attribute.Text;
    label_web_en: Attribute.Text;
    image: Attribute.Media;
    original_price: Attribute.BigInteger;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::service.service',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::service.service',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiServiceBundleServiceBundle extends Schema.CollectionType {
  collectionName: 'service_bundles';
  info: {
    singularName: 'service-bundle';
    pluralName: 'service-bundles';
    displayName: 'Service Bundle';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    medical_services: Attribute.Relation<
      'api::service-bundle.service-bundle',
      'manyToMany',
      'api::medical-service.medical-service'
    >;
    label: Attribute.String;
    price: Attribute.Integer;
    Locations: Attribute.Component<
      'medical-service-setting.medical-service-setting',
      true
    >;
    label_i: Attribute.String;
    membership_discount: Attribute.Component<'membership-discount.membership-discount'>;
    tags: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::service-bundle.service-bundle',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::service-bundle.service-bundle',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiServiceJsonServiceJson extends Schema.SingleType {
  collectionName: 'service_jsons';
  info: {
    singularName: 'service-json';
    pluralName: 'service-jsons';
    displayName: 'Service Json';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    data: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::service-json.service-json',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::service-json.service-json',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSubPackageSubPackage extends Schema.CollectionType {
  collectionName: 'sub_packages';
  info: {
    singularName: 'sub-package';
    pluralName: 'sub-packages';
    displayName: 'Sub Package';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    label: Attribute.Text;
    image: Attribute.Media;
    package: Attribute.Relation<
      'api::sub-package.sub-package',
      'manyToOne',
      'api::package.package'
    >;
    services: Attribute.Relation<
      'api::sub-package.sub-package',
      'oneToMany',
      'api::service.service'
    >;
    en_label: Attribute.Text;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::sub-package.sub-package',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::sub-package.sub-package',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiSubscriberSubscriber extends Schema.CollectionType {
  collectionName: 'subscribers';
  info: {
    singularName: 'subscriber';
    pluralName: 'subscribers';
    displayName: 'Subscriber';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    email: Attribute.Email;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::subscriber.subscriber',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::subscriber.subscriber',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTagifyWhitelistTagifyWhitelist extends Schema.SingleType {
  collectionName: 'tagify_whitelists';
  info: {
    singularName: 'tagify-whitelist';
    pluralName: 'tagify-whitelists';
    displayName: 'TagifyWhitelist';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    data: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::tagify-whitelist.tagify-whitelist',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::tagify-whitelist.tagify-whitelist',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

export interface ApiTaskTask extends Schema.CollectionType {
  collectionName: 'tasks';
  info: {
    singularName: 'task';
    pluralName: 'tasks';
    displayName: 'Task';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    title: Attribute.String;
    board: Attribute.Relation<
      'api::task.task',
      'manyToOne',
      'api::board.board'
    >;
    list: Attribute.Relation<'api::task.task', 'manyToOne', 'api::list.list'>;
    uid: Attribute.String;
    description: Attribute.RichText;
    attachments: Attribute.JSON;
    labels: Attribute.JSON;
    comments: Attribute.JSON;
    assigments: Attribute.JSON;
    coverImage: Attribute.String;
    members: Attribute.Relation<
      'api::task.task',
      'manyToMany',
      'plugin::users-permissions.user'
    >;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<'api::task.task', 'oneToOne', 'admin::user'> &
      Attribute.Private;
    updatedBy: Attribute.Relation<'api::task.task', 'oneToOne', 'admin::user'> &
      Attribute.Private;
  };
}

export interface ApiTransactionTransaction extends Schema.CollectionType {
  collectionName: 'transactions';
  info: {
    singularName: 'transaction';
    pluralName: 'transactions';
    displayName: 'Transaction';
    description: '';
  };
  options: {
    draftAndPublish: true;
  };
  attributes: {
    amount: Attribute.BigInteger;
    type: Attribute.Enumeration<['income', 'expense']>;
    paymentMethod: Attribute.Enumeration<
      [
        'cash',
        'bankTransfer',
        'visa',
        'mastercard',
        'member-card',
        'service-card'
      ]
    >;
    billingType: Attribute.Enumeration<
      [
        'member-card',
        'service-card',
        'treatment',
        'order',
        'product',
        'debt-collection',
        'card-canceled'
      ]
    >;
    note: Attribute.String;
    discount: Attribute.BigInteger;
    vat: Attribute.Float;
    subTotal: Attribute.BigInteger;
    total: Attribute.BigInteger;
    change: Attribute.BigInteger;
    debtBalance: Attribute.BigInteger;
    code: Attribute.String;
    serviceCardUsaged: Attribute.Integer;
    serviceCardLimit: Attribute.Integer;
    purchase: Attribute.BigInteger;
    interestMoney: Attribute.BigInteger & Attribute.DefaultTo<'0'>;
    status: Attribute.Enumeration<
      ['new', 'progress', 'done', 'paid', 'confirmed']
    > &
      Attribute.DefaultTo<'new'>;
    startedTreatmentAt: Attribute.DateTime;
    endedTreatmentAt: Attribute.DateTime;
    products: Attribute.JSON;
    createdAt: Attribute.DateTime;
    updatedAt: Attribute.DateTime;
    publishedAt: Attribute.DateTime;
    createdBy: Attribute.Relation<
      'api::transaction.transaction',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
    updatedBy: Attribute.Relation<
      'api::transaction.transaction',
      'oneToOne',
      'admin::user'
    > &
      Attribute.Private;
  };
}

declare module '@strapi/strapi' {
  export module Shared {
    export interface ContentTypes {
      'admin::permission': AdminPermission;
      'admin::user': AdminUser;
      'admin::role': AdminRole;
      'admin::api-token': AdminApiToken;
      'admin::api-token-permission': AdminApiTokenPermission;
      'admin::transfer-token': AdminTransferToken;
      'admin::transfer-token-permission': AdminTransferTokenPermission;
      'plugin::upload.file': PluginUploadFile;
      'plugin::upload.folder': PluginUploadFolder;
      'plugin::users-permissions.permission': PluginUsersPermissionsPermission;
      'plugin::users-permissions.role': PluginUsersPermissionsRole;
      'plugin::users-permissions.user': PluginUsersPermissionsUser;
      'plugin::i18n.locale': PluginI18NLocale;
      'api::about.about': ApiAboutAbout;
      'api::article.article': ApiArticleArticle;
      'api::banner.banner': ApiBannerBanner;
      'api::blog.blog': ApiBlogBlog;
      'api::board.board': ApiBoardBoard;
      'api::booking.booking': ApiBookingBooking;
      'api::bundle-exam.bundle-exam': ApiBundleExamBundleExam;
      'api::bundle-serices-json.bundle-serices-json': ApiBundleSericesJsonBundleSericesJson;
      'api::bundle-service-usage.bundle-service-usage': ApiBundleServiceUsageBundleServiceUsage;
      'api::camera.camera': ApiCameraCamera;
      'api::cart.cart': ApiCartCart;
      'api::cart-line.cart-line': ApiCartLineCartLine;
      'api::character.character': ApiCharacterCharacter;
      'api::check-in.check-in': ApiCheckInCheckIn;
      'api::chronic-service.chronic-service': ApiChronicServiceChronicService;
      'api::conversation.conversation': ApiConversationConversation;
      'api::conversation-queue.conversation-queue': ApiConversationQueueConversationQueue;
      'api::discount-setting.discount-setting': ApiDiscountSettingDiscountSetting;
      'api::drug.drug': ApiDrugDrug;
      'api::email-template.email-template': ApiEmailTemplateEmailTemplate;
      'api::event-log.event-log': ApiEventLogEventLog;
      'api::examination.examination': ApiExaminationExamination;
      'api::global-setting.global-setting': ApiGlobalSettingGlobalSetting;
      'api::index-image.index-image': ApiIndexImageIndexImage;
      'api::invoice.invoice': ApiInvoiceInvoice;
      'api::list.list': ApiListList;
      'api::log.log': ApiLogLog;
      'api::medical-record.medical-record': ApiMedicalRecordMedicalRecord;
      'api::medical-service.medical-service': ApiMedicalServiceMedicalService;
      'api::medical-service-usage.medical-service-usage': ApiMedicalServiceUsageMedicalServiceUsage;
      'api::medicine.medicine': ApiMedicineMedicine;
      'api::membership-card.membership-card': ApiMembershipCardMembershipCard;
      'api::order.order': ApiOrderOrder;
      'api::otp.otp': ApiOtpOtp;
      'api::package.package': ApiPackagePackage;
      'api::patient.patient': ApiPatientPatient;
      'api::patient-source.patient-source': ApiPatientSourcePatientSource;
      'api::prescription.prescription': ApiPrescriptionPrescription;
      'api::product.product': ApiProductProduct;
      'api::reason-list.reason-list': ApiReasonListReasonList;
      'api::room.room': ApiRoomRoom;
      'api::service.service': ApiServiceService;
      'api::service-bundle.service-bundle': ApiServiceBundleServiceBundle;
      'api::service-json.service-json': ApiServiceJsonServiceJson;
      'api::sub-package.sub-package': ApiSubPackageSubPackage;
      'api::subscriber.subscriber': ApiSubscriberSubscriber;
      'api::tagify-whitelist.tagify-whitelist': ApiTagifyWhitelistTagifyWhitelist;
      'api::task.task': ApiTaskTask;
      'api::transaction.transaction': ApiTransactionTransaction;
    }
  }
}
