import type { Schema, Attribute } from '@strapi/strapi';

export interface MedicalServiceSettingMedicalServiceSetting
  extends Schema.Component {
  collectionName: 'components_medical_service_setting_medical_service_settings';
  info: {
    displayName: 'Medical-service-setting';
    description: '';
  };
  attributes: {
    location: Attribute.Enumeration<['q2', 'q7', 'binhduong']>;
    price: Attribute.BigInteger;
    disabled: Attribute.Boolean;
  };
}

export interface MembershipDiscountMembershipDiscount extends Schema.Component {
  collectionName: 'components_membership_discount_membership_discounts';
  info: {
    displayName: 'Membership Discount';
    description: '';
  };
  attributes: {
    gold_percentage: Attribute.Integer;
    platinum_percentage: Attribute.Integer;
    gold_monthly: Attribute.Integer;
    platinum_monthly: Attribute.Integer;
    gold_yearly: Attribute.Integer;
    platinum_yearly: Attribute.Integer;
    medical_provider_percentage: Attribute.Integer;
    medical_provider_monthly: Attribute.Integer;
    medical_provider_yearly: Attribute.Integer;
    medical_provider_gold_percentage: Attribute.Integer;
    medical_provider_gold_monthly: Attribute.Integer;
    medical_provider_gold_yearly: Attribute.Integer;
    medical_provider_platinum_percentage: Attribute.Integer;
    medical_provider_platinum_monthly: Attribute.Integer;
    medical_provider_platinum_yearly: Attribute.Integer;
    infant_percentage: Attribute.Integer;
    infant_monthly: Attribute.Integer;
    infant_yearly: Attribute.Integer;
    toddler_percentage: Attribute.Integer;
    toddler_monthly: Attribute.Integer;
    toddler_yearly: Attribute.Integer;
    preschool_school_age_percentage: Attribute.Integer;
    preschool_school_age_monthly: Attribute.Integer;
    preschool_school_age_yearly: Attribute.Integer;
  };
}

export interface PrescriptionDrugPrescriptionDrug extends Schema.Component {
  collectionName: 'components_prescription_drug_prescription_drugs';
  info: {
    displayName: 'Prescription Drug';
    description: '';
  };
  attributes: {
    drug: Attribute.Relation<
      'prescription-drug.prescription-drug',
      'oneToOne',
      'api::drug.drug'
    >;
    amount: Attribute.Decimal;
    morningAmount: Attribute.Decimal;
    noonAmount: Attribute.Decimal;
    afternoonAmount: Attribute.Decimal;
    eveningAmount: Attribute.Decimal;
    unit: Attribute.String;
    usage: Attribute.Text;
    numberOfDays: Attribute.Decimal;
    price: Attribute.Integer;
  };
}

export interface RelationshipRelationship extends Schema.Component {
  collectionName: 'components_relationship_relationships';
  info: {
    displayName: 'Relationship';
    icon: 'ad';
  };
  attributes: {
    label: Attribute.String;
    patient: Attribute.Relation<
      'relationship.relationship',
      'oneToOne',
      'api::patient.patient'
    >;
  };
}

export interface ServiceService extends Schema.Component {
  collectionName: 'components_service_services';
  info: {
    displayName: 'Service';
    icon: 'attachment';
    description: '';
  };
  attributes: {
    medical_service: Attribute.Relation<
      'service.service',
      'oneToOne',
      'api::medical-service.medical-service'
    >;
    count: Attribute.Integer;
  };
}

declare module '@strapi/strapi' {
  export module Shared {
    export interface Components {
      'medical-service-setting.medical-service-setting': MedicalServiceSettingMedicalServiceSetting;
      'membership-discount.membership-discount': MembershipDiscountMembershipDiscount;
      'prescription-drug.prescription-drug': PrescriptionDrugPrescriptionDrug;
      'relationship.relationship': RelationshipRelationship;
      'service.service': ServiceService;
    }
  }
}
